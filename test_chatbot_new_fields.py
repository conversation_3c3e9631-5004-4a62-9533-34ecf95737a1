#!/usr/bin/env python3
"""
Test script to verify the new chatbot fields functionality:
- welcomeMessage
- thankYouMessage  
- connectedAccountId

This script tests the database model, Pydantic models, and API endpoints.
"""

import json
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_database_model():
    """Test that the database model includes the new fields"""
    print("Testing database model...")
    
    try:
        from app.models import Chatbot
        
        # Check if the new fields exist in the model
        model_fields = [attr for attr in dir(Chatbot) if not attr.startswith('_')]
        
        required_fields = ['welcome_message', 'thank_you_message', 'connected_account_id']
        found_fields = []
        
        for field in required_fields:
            if field in model_fields:
                found_fields.append(field)
                print(f"  ✓ {field} field found in Chatbot model")
            else:
                print(f"  ✗ {field} field not found in Chatbot model")
        
        if len(found_fields) == len(required_fields):
            print("✓ Database model test passed")
            return True
        else:
            print(f"✗ Database model test failed - {len(required_fields) - len(found_fields)} fields missing")
            return False
            
    except Exception as e:
        print(f"✗ Database model test failed: {str(e)}")
        return False


def test_pydantic_models():
    """Test that the Pydantic models include the new fields"""
    print("\nTesting Pydantic models...")
    
    try:
        from app.models import ChatbotCreate, ChatbotUpdate
        
        # Test ChatbotCreate model
        create_fields = ChatbotCreate.__fields__.keys()
        required_fields = ['welcome_message', 'thank_you_message', 'connected_account_id']
        
        create_found = []
        for field in required_fields:
            if field in create_fields:
                create_found.append(field)
                print(f"  ✓ {field} field found in ChatbotCreate model")
            else:
                print(f"  ✗ {field} field not found in ChatbotCreate model")
        
        # Test ChatbotUpdate model
        update_fields = ChatbotUpdate.__fields__.keys()
        update_found = []
        for field in required_fields:
            if field in update_fields:
                update_found.append(field)
                print(f"  ✓ {field} field found in ChatbotUpdate model")
            else:
                print(f"  ✗ {field} field not found in ChatbotUpdate model")
        
        if len(create_found) == len(required_fields) and len(update_found) == len(required_fields):
            print("✓ Pydantic models test passed")
            return True
        else:
            print("✗ Pydantic models test failed")
            return False
            
    except Exception as e:
        print(f"✗ Pydantic models test failed: {str(e)}")
        return False


def test_model_validation():
    """Test that the Pydantic models validate correctly"""
    print("\nTesting model validation...")
    
    try:
        from app.models import ChatbotCreate, ChatbotUpdate
        
        # Test ChatbotCreate with new fields
        create_data = {
            "name": "Test Chatbot",
            "type": "AI",
            "description": "Test description",
            "welcome_message": "Welcome to our service!",
            "thank_you_message": "Thank you for using our service!",
            "connected_account_id": "account_123"
        }
        
        chatbot_create = ChatbotCreate(**create_data)
        print(f"  ✓ ChatbotCreate validation passed")
        print(f"    - welcome_message: {chatbot_create.welcome_message}")
        print(f"    - thank_you_message: {chatbot_create.thank_you_message}")
        print(f"    - connected_account_id: {chatbot_create.connected_account_id}")
        
        # Test ChatbotUpdate with new fields
        update_data = {
            "name": "Updated Chatbot",
            "welcome_message": "Updated welcome message",
            "thank_you_message": "Updated thank you message",
            "connected_account_id": "updated_account_456"
        }
        
        chatbot_update = ChatbotUpdate(**update_data)
        print(f"  ✓ ChatbotUpdate validation passed")
        print(f"    - welcome_message: {chatbot_update.welcome_message}")
        print(f"    - thank_you_message: {chatbot_update.thank_you_message}")
        print(f"    - connected_account_id: {chatbot_update.connected_account_id}")
        
        # Test with None values (should be allowed)
        create_with_none = ChatbotCreate(
            name="Test Chatbot",
            type="AI",
            welcome_message=None,
            thank_you_message=None,
            connected_account_id=None
        )
        print(f"  ✓ ChatbotCreate with None values validation passed")
        
        print("✓ Model validation test passed")
        return True
        
    except Exception as e:
        print(f"✗ Model validation test failed: {str(e)}")
        return False


def test_api_request_structure():
    """Test the structure of API requests"""
    print("\nTesting API request structure...")
    
    try:
        # Test create chatbot request structure
        create_request = {
            "name": "Test Chatbot API",
            "type": "AI",
            "description": "API test description",
            "welcome_message": "Welcome! How can I help you today?",
            "thank_you_message": "Thank you for chatting with us!",
            "connected_account_id": "api_account_789"
        }
        
        print("  ✓ Create chatbot request structure:")
        for key, value in create_request.items():
            print(f"    - {key}: {value}")
        
        # Test update chatbot request structure
        update_request = {
            "name": "Updated API Chatbot",
            "description": "Updated API description",
            "welcome_message": "Updated welcome! How may I assist you?",
            "thank_you_message": "Updated thank you for your time!",
            "connected_account_id": "updated_api_account_999"
        }
        
        print("  ✓ Update chatbot request structure:")
        for key, value in update_request.items():
            print(f"    - {key}: {value}")
        
        # Test partial update request
        partial_update = {
            "welcome_message": "New welcome message only"
        }
        
        print("  ✓ Partial update request structure:")
        for key, value in partial_update.items():
            print(f"    - {key}: {value}")
        
        print("✓ API request structure test passed")
        return True
        
    except Exception as e:
        print(f"✗ API request structure test failed: {str(e)}")
        return False


def test_migration_script():
    """Test that the migration script exists and has correct structure"""
    print("\nTesting migration script...")
    
    try:
        migration_file = "migrations/add_chatbot_message_fields.py"
        
        if not os.path.exists(migration_file):
            print(f"✗ Migration file not found: {migration_file}")
            return False
        
        with open(migration_file, 'r') as f:
            content = f.read()
        
        required_elements = [
            "ALTER TABLE chatbots",
            "ADD COLUMN IF NOT EXISTS welcome_message",
            "ADD COLUMN IF NOT EXISTS thank_you_message", 
            "ADD COLUMN IF NOT EXISTS connected_account_id",
            "def run_migration",
            "def rollback_migration",
            "def check_migration_needed"
        ]
        
        found_elements = []
        for element in required_elements:
            if element in content:
                found_elements.append(element)
                print(f"  ✓ {element} found in migration script")
            else:
                print(f"  ✗ {element} not found in migration script")
        
        if len(found_elements) == len(required_elements):
            print("✓ Migration script test passed")
            return True
        else:
            print(f"✗ Migration script test failed - {len(required_elements) - len(found_elements)} elements missing")
            return False
            
    except Exception as e:
        print(f"✗ Migration script test failed: {str(e)}")
        return False


def test_api_response_structure():
    """Test expected API response structure"""
    print("\nTesting API response structure...")
    
    try:
        # Expected response structure for create/get/update chatbot
        expected_response = {
            "id": "chatbot_id_here",
            "tenant_id": "tenant_id_here",
            "name": "Chatbot Name",
            "type": "AI",
            "description": "Chatbot description",
            "welcome_message": "Welcome message",
            "thank_you_message": "Thank you message",
            "connected_account_id": "account_id",
            "status": "DRAFT",
            "created_at": "timestamp",
            "updated_at": "timestamp"
        }
        
        print("  ✓ Expected API response structure:")
        for key, value in expected_response.items():
            print(f"    - {key}: {type(value).__name__}")
        
        # Check that new fields are included
        new_fields = ["welcome_message", "thank_you_message", "connected_account_id"]
        for field in new_fields:
            if field in expected_response:
                print(f"  ✓ {field} included in response structure")
            else:
                print(f"  ✗ {field} missing from response structure")
        
        print("✓ API response structure test passed")
        return True
        
    except Exception as e:
        print(f"✗ API response structure test failed: {str(e)}")
        return False


def test_field_constraints():
    """Test field constraints and validation"""
    print("\nTesting field constraints...")
    
    try:
        from app.models import ChatbotCreate
        
        # Test that fields are optional (can be None)
        chatbot_minimal = ChatbotCreate(
            name="Minimal Chatbot",
            type="AI"
        )
        
        print("  ✓ Fields are optional - minimal chatbot created")
        print(f"    - welcome_message: {chatbot_minimal.welcome_message}")
        print(f"    - thank_you_message: {chatbot_minimal.thank_you_message}")
        print(f"    - connected_account_id: {chatbot_minimal.connected_account_id}")
        
        # Test with empty strings
        chatbot_empty = ChatbotCreate(
            name="Empty Fields Chatbot",
            type="AI",
            welcome_message="",
            thank_you_message="",
            connected_account_id=""
        )
        
        print("  ✓ Empty string values accepted")
        
        # Test with long strings
        long_message = "A" * 1000  # 1000 character string
        chatbot_long = ChatbotCreate(
            name="Long Fields Chatbot",
            type="AI",
            welcome_message=long_message,
            thank_you_message=long_message,
            connected_account_id="long_account_id_" + "x" * 100
        )
        
        print("  ✓ Long string values accepted")
        
        print("✓ Field constraints test passed")
        return True
        
    except Exception as e:
        print(f"✗ Field constraints test failed: {str(e)}")
        return False


def main():
    """Run all tests for the new chatbot fields"""
    print("=" * 70)
    print("Chatbot New Fields Test Suite")
    print("=" * 70)
    
    tests = [
        test_database_model,
        test_pydantic_models,
        test_model_validation,
        test_api_request_structure,
        test_migration_script,
        test_api_response_structure,
        test_field_constraints
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with error: {str(e)}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! New chatbot fields are ready.")
        print("\nNew Fields Added:")
        print("  • welcomeMessage: Optional welcome message for users")
        print("  • thankYouMessage: Optional thank you message after conversations")
        print("  • connectedAccountId: Optional ID linking chatbot to external account")
        print("\nAPI Changes:")
        print("  • POST /chatbots - accepts new fields in request body")
        print("  • PUT /chatbots/{id} - accepts new fields for updates")
        print("  • GET /chatbots/{id} - returns new fields in response")
        print("\nDatabase Changes:")
        print("  • Migration script created: migrations/add_chatbot_message_fields.py")
        print("  • Run migration before deploying: python migrations/add_chatbot_message_fields.py")
    else:
        print("✗ Some tests failed. Please review the implementation.")
    
    print("=" * 70)


if __name__ == "__main__":
    main()
