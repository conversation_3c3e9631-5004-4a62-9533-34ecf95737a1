import importlib.util
import os
from pathlib import Path
import sys

def run_migrations():
    """Run all migration scripts in the migrations directory"""
    # Get the directory of this script
    script_dir = Path(__file__).parent
    
    # Path to migrations directory
    migrations_dir = script_dir / "migrations"
    
    # Ensure migrations directory exists
    if not migrations_dir.exists():
        print(f"Migrations directory not found: {migrations_dir}")
        sys.exit(1)
    
    # Get all Python files in the migrations directory
    migration_files = sorted([f for f in migrations_dir.glob("*.py") if f.is_file()])
    
    print(f"Found {len(migration_files)} migration files.")
    
    # Run each migration
    for migration_file in migration_files:
        print(f"\nRunning migration: {migration_file.name}")
        
        # Load the module
        spec = importlib.util.spec_from_file_location(
            migration_file.stem, migration_file
        )
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Run the migration
        if hasattr(module, "run_migration"):
            module.run_migration()
        else:
            print(f"Warning: {migration_file.name} does not have a run_migration function.")

if __name__ == "__main__":
    run_migrations()