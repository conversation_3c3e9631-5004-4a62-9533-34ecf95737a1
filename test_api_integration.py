#!/usr/bin/env python3
"""
API Integration Test for Multiple File Upload
This demonstrates how to use the multiple file upload endpoint
"""

import requests
import json
from io import BytesIO


def create_sample_pdf_content():
    """Create sample PDF content for testing"""
    # This is a minimal PDF structure for testing
    # In real usage, you would use actual PDF files
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Sample PDF content) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF"""
    return pdf_content


def test_multiple_file_upload_api():
    """
    Test the multiple file upload API endpoint
    
    This is a demonstration of how to use the API.
    In a real environment, you would:
    1. Have a running FastAPI server
    2. Use actual authentication tokens
    3. Use real PDF files
    """
    
    print("Multiple File Upload API Integration Test")
    print("=" * 50)
    
    # API Configuration
    BASE_URL = "http://localhost:8000"  # Adjust to your server URL
    CHATBOT_ID = "123e4567-e89b-12d3-a456-426614174000"  # Example chatbot ID
    
    # Headers (in real usage, include authentication)
    headers = {
        "Authorization": "Bearer your-jwt-token-here"
    }
    
    # Create sample files for upload
    files_to_upload = []
    
    # File 1: Valid PDF
    pdf_content1 = create_sample_pdf_content()
    files_to_upload.append(
        ("files", ("document1.pdf", BytesIO(pdf_content1), "application/pdf"))
    )
    
    # File 2: Another valid PDF
    pdf_content2 = create_sample_pdf_content()
    files_to_upload.append(
        ("files", ("document2.pdf", BytesIO(pdf_content2), "application/pdf"))
    )
    
    # File 3: Invalid file type (for testing validation)
    text_content = b"This is a text file, not a PDF"
    files_to_upload.append(
        ("files", ("invalid.txt", BytesIO(text_content), "text/plain"))
    )
    
    print(f"Uploading {len(files_to_upload)} files to chatbot {CHATBOT_ID}")
    print("Files:")
    for _, (filename, _, content_type) in files_to_upload:
        print(f"  - {filename} ({content_type})")
    
    # API Endpoint
    url = f"{BASE_URL}/chatbot/{CHATBOT_ID}/knowledgebase"
    
    try:
        # Make the API request
        print(f"\nMaking POST request to: {url}")
        
        # Note: This will fail without a running server, but demonstrates the usage
        response = requests.post(
            url,
            files=files_to_upload,
            headers=headers,
            timeout=30
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("\n✓ Upload Successful!")
            print(f"Total files processed: {result['total_files']}")
            print(f"Successful uploads: {result['successful_uploads']}")
            print(f"Failed uploads: {result['failed_uploads']}")
            
            if result['results']['successful']:
                print("\nSuccessful uploads:")
                for success in result['results']['successful']:
                    print(f"  ✓ {success['filename']} -> {success['document_id']}")
            
            if result['results']['failed']:
                print("\nFailed uploads:")
                for failure in result['results']['failed']:
                    print(f"  ✗ {failure['filename']}: {failure['error']}")
            
            print(f"\nChatbot status: {result['chatbot_status']}")
            
        else:
            print(f"✗ Upload Failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("✗ Connection Error: Server not running")
        print("\nTo test this API:")
        print("1. Start your FastAPI server")
        print("2. Update the BASE_URL and authentication")
        print("3. Run this script again")
        
    except Exception as e:
        print(f"✗ Error: {str(e)}")


def demonstrate_curl_usage():
    """Demonstrate how to use the API with curl"""
    
    print("\n" + "=" * 50)
    print("cURL Usage Examples")
    print("=" * 50)
    
    chatbot_id = "123e4567-e89b-12d3-a456-426614174000"
    
    print("\n1. Upload single file:")
    print(f"""curl -X POST \\
  "http://localhost:8000/chatbot/{chatbot_id}/knowledgebase" \\
  -H "Authorization: Bearer your-jwt-token" \\
  -F "files=@document.pdf" """)
    
    print("\n2. Upload multiple files:")
    print(f"""curl -X POST \\
  "http://localhost:8000/chatbot/{chatbot_id}/knowledgebase" \\
  -H "Authorization: Bearer your-jwt-token" \\
  -F "files=@document1.pdf" \\
  -F "files=@document2.pdf" \\
  -F "files=@document3.pdf" """)
    
    print("\n3. Expected response format:")
    print("""{
  "message": "Processed 3 files",
  "chatbot_id": "123e4567-e89b-12d3-a456-426614174000",
  "tenant_id": "tenant-123",
  "total_files": 3,
  "successful_uploads": 2,
  "failed_uploads": 1,
  "results": {
    "successful": [
      {
        "filename": "document1.pdf",
        "status": "success",
        "document_id": "doc-uuid-1",
        "s3_key": "tenant-123/chatbot-123/document1.pdf",
        "es_index": "documents_tenant_123"
      }
    ],
    "failed": [
      {
        "filename": "invalid.txt",
        "status": "failed",
        "error": "Only PDF files are supported"
      }
    ]
  },
  "chatbot_status": "ACTIVE"
}""")


def demonstrate_javascript_usage():
    """Demonstrate how to use the API with JavaScript"""
    
    print("\n" + "=" * 50)
    print("JavaScript Usage Example")
    print("=" * 50)
    
    js_code = '''
// Multiple File Upload with JavaScript
async function uploadMultipleFiles(chatbotId, files) {
    const formData = new FormData();
    
    // Add each file to the form data
    for (let i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
    }
    
    try {
        const response = await fetch(`/chatbot/${chatbotId}/knowledgebase`, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + getAuthToken()
            },
            body: formData
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log(`✓ Processed ${result.total_files} files`);
            console.log(`✓ Successful: ${result.successful_uploads}`);
            console.log(`✗ Failed: ${result.failed_uploads}`);
            
            // Handle successful uploads
            result.results.successful.forEach(file => {
                console.log(`✓ ${file.filename} uploaded successfully`);
            });
            
            // Handle failed uploads
            result.results.failed.forEach(file => {
                console.error(`✗ ${file.filename}: ${file.error}`);
            });
            
            return result;
        } else {
            throw new Error(`Upload failed: ${response.status}`);
        }
    } catch (error) {
        console.error('Upload error:', error);
        throw error;
    }
}

// Usage with file input
document.getElementById('fileInput').addEventListener('change', async (event) => {
    const files = event.target.files;
    const chatbotId = 'your-chatbot-id';
    
    if (files.length > 0) {
        try {
            const result = await uploadMultipleFiles(chatbotId, files);
            // Handle success
        } catch (error) {
            // Handle error
        }
    }
});
'''
    
    print(js_code)


def demonstrate_python_usage():
    """Demonstrate how to use the API with Python requests"""
    
    print("\n" + "=" * 50)
    print("Python Usage Example")
    print("=" * 50)
    
    python_code = '''
import requests
from pathlib import Path

def upload_multiple_files(chatbot_id, file_paths, auth_token):
    """
    Upload multiple files to chatbot knowledgebase
    
    Args:
        chatbot_id (str): The chatbot ID
        file_paths (list): List of file paths to upload
        auth_token (str): JWT authentication token
    
    Returns:
        dict: Upload results
    """
    url = f"http://localhost:8000/chatbot/{chatbot_id}/knowledgebase"
    
    headers = {
        "Authorization": f"Bearer {auth_token}"
    }
    
    # Prepare files for upload
    files = []
    for file_path in file_paths:
        path = Path(file_path)
        if path.exists() and path.suffix.lower() == '.pdf':
            files.append(
                ('files', (path.name, open(path, 'rb'), 'application/pdf'))
            )
    
    try:
        response = requests.post(url, files=files, headers=headers)
        
        # Close file handles
        for _, (_, file_handle, _) in files:
            file_handle.close()
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Upload failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        # Close file handles in case of error
        for _, (_, file_handle, _) in files:
            if not file_handle.closed:
                file_handle.close()
        raise e

# Usage example
if __name__ == "__main__":
    chatbot_id = "123e4567-e89b-12d3-a456-426614174000"
    file_paths = [
        "document1.pdf",
        "document2.pdf", 
        "document3.pdf"
    ]
    auth_token = "your-jwt-token"
    
    try:
        result = upload_multiple_files(chatbot_id, file_paths, auth_token)
        print(f"✓ Upload completed: {result['successful_uploads']} successful, {result['failed_uploads']} failed")
    except Exception as e:
        print(f"✗ Upload failed: {e}")
'''
    
    print(python_code)


def main():
    """Run the API integration demonstration"""
    
    print("Multiple File Upload API Integration Guide")
    print("=" * 60)
    
    # Test the API (will show connection error without running server)
    test_multiple_file_upload_api()
    
    # Show usage examples
    demonstrate_curl_usage()
    demonstrate_javascript_usage()
    demonstrate_python_usage()
    
    print("\n" + "=" * 60)
    print("Integration Test Complete")
    print("\nKey Features Demonstrated:")
    print("  • Multiple file upload in single request")
    print("  • Individual file validation and error handling")
    print("  • Detailed success/failure reporting")
    print("  • Support for various client implementations")
    print("  • Proper error handling and status codes")
    print("=" * 60)


if __name__ == "__main__":
    main()
