-- Migration script to add missing columns to chatbots table
-- This script adds the following columns:
-- - welcome_message (VARCHAR, nullable)
-- - thank_you_message (VARCHAR, nullable) 
-- - connected_account_id (VARCHAR, nullable)

-- Start transaction
BEGIN;

-- Check if chatbots table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'chatbots') THEN
        RAISE EXCEPTION 'Table chatbots does not exist. Please create the table first.';
    END IF;
END $$;

-- Add welcome_message column if it doesn't exist
ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS welcome_message VARCHAR;

-- Add thank_you_message column if it doesn't exist
ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS thank_you_message VARCHAR;

-- Add connected_account_id column if it doesn't exist
ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS connected_account_id VARCHAR;

-- Verify columns using DO block for detailed feedback
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'chatbots' AND column_name = 'welcome_message'
    ) THEN
        RAISE NOTICE 'WARNING: welcome_message column was not added';
    ELSE
        RAISE NOTICE 'SUCCESS: welcome_message column exists';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'chatbots' AND column_name = 'thank_you_message'
    ) THEN
        RAISE NOTICE 'WARNING: thank_you_message column was not added';
    ELSE
        RAISE NOTICE 'SUCCESS: thank_you_message column exists';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'chatbots' AND column_name = 'connected_account_id'
    ) THEN
        RAISE NOTICE 'WARNING: connected_account_id column was not added';
    ELSE
        RAISE NOTICE 'SUCCESS: connected_account_id column exists';
    END IF;
END $$;

-- Verify all columns were added
DO $$
DECLARE
    column_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_name = 'chatbots' 
    AND column_name IN ('welcome_message', 'thank_you_message', 'connected_account_id');
    
    IF column_count = 3 THEN
        RAISE NOTICE 'SUCCESS: All 3 columns are present in chatbots table';
    ELSE
        RAISE NOTICE 'WARNING: Only % out of 3 expected columns found', column_count;
    END IF;
END $$;

-- Show the current structure of chatbots table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'chatbots'
ORDER BY ordinal_position;

-- Commit the transaction
COMMIT;

-- Final verification message
SELECT 'Migration completed successfully. The chatbots table now has the required columns.' AS status;
