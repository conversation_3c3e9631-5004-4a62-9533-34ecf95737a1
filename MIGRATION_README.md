# Conversation Token Usage Migration Guide

This guide explains how to migrate your database to support the new JSONB-based conversation token usage tracking.

## Overview

The new implementation stores conversation data in JSONB columns for better flexibility and querying capabilities:

- **Old Structure**: Separate rows for user/assistant messages with `message_type` and `message_content`
- **New Structure**: Single row per conversation turn with `input` (JSONB) and `output` (JSONB) columns

## Migration Scripts

### 1. Automatic Migration Runner (Recommended)

```bash
python3 run_token_usage_migration.py
```

This script automatically detects your current setup and runs the appropriate migration:

- **Fresh Installation**: Creates new table with JSONB structure
- **Existing Old Structure**: Backs up data, updates structure, migrates data
- **Already Updated**: Verifies structure and skips migration

### 2. Manual Migration Scripts

#### For Fresh Installations
```bash
python3 migrations/add_conversation_token_usage.py
```

#### For Updating Existing Tables
```bash
python3 migrations/update_conversation_token_usage_to_jsonb.py
```

### 3. Test Migration
```bash
python3 test_migration.py
```

Verifies that the migration completed successfully and tests JSONB functionality.

## Migration Process

### Automatic Detection

The migration runner checks your current table status:

1. **Table doesn't exist** → Runs fresh installation
2. **Old structure detected** → Runs structure update with data migration
3. **JSONB structure exists** → Verifies and skips migration
4. **Unknown structure** → Prompts for manual review

### Data Migration

For existing installations, the migration:

1. **Backs up** existing data to `conversation_token_usage_backup`
2. **Drops** the old table structure
3. **Creates** new table with JSONB columns
4. **Migrates** old data to new structure:
   - Groups user/assistant message pairs into conversation turns
   - Creates JSONB input: `{"user": "message", "system": "prompt"}`
   - Creates JSONB output: `{"assistant": "response"}`
   - Preserves token counts and timestamps

### Safety Features

- **Automatic backup** before any destructive operations
- **Transaction safety** - all changes are rolled back on error
- **Data validation** after migration
- **Verification tests** to ensure everything works

## New Table Structure

```sql
CREATE TABLE conversation_token_usage (
    id VARCHAR PRIMARY KEY,
    conversation_id VARCHAR REFERENCES chatbot_conversations(id),
    tenant_id VARCHAR,
    input JSONB,                 -- {"user": "message", "system": "prompt"}
    output JSONB,                -- {"assistant": "response"}
    input_tokens INTEGER DEFAULT 0,
    output_tokens INTEGER DEFAULT 0,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Indexes Created

- `conversation_id` - Fast lookups by conversation
- `tenant_id` - Fast lookups by tenant
- `input` (GIN) - Fast JSONB queries on input data
- `output` (GIN) - Fast JSONB queries on output data

## Usage Examples

### Storing Data

```python
from app.routers.chatbot import store_conversation_turn

store_conversation_turn(
    db, conversation_id, tenant_id,
    user_message="What are your hours?",
    system_prompt="You are a helpful assistant.",
    ai_response="We're open 9 AM to 5 PM.",
    input_tokens=20,
    output_tokens=12
)
```

### Querying Data

```sql
-- Get user messages
SELECT input->>'user' as user_message 
FROM conversation_token_usage;

-- Get AI responses
SELECT output->>'assistant' as ai_response 
FROM conversation_token_usage;

-- Search in user messages
SELECT * FROM conversation_token_usage 
WHERE input->>'user' ILIKE '%business hours%';

-- Get conversation turns with token counts
SELECT 
    input->>'user' as user_message,
    output->>'assistant' as ai_response,
    input_tokens,
    output_tokens,
    timestamp
FROM conversation_token_usage 
WHERE conversation_id = 'your-conversation-id'
ORDER BY timestamp;
```

## Troubleshooting

### Migration Fails

1. **Check database connection**:
   ```bash
   # Test connection
   psql -h localhost -U sdwhatsapp -d sdwhatsapp -c "SELECT 1;"
   ```

2. **Check environment variables**:
   ```bash
   echo $POSTGRES_USER
   echo $POSTGRES_PASSWORD
   echo $POSTGRES_HOST
   echo $POSTGRES_DB
   ```

3. **Run test script**:
   ```bash
   python3 test_migration.py
   ```

### Data Recovery

If migration fails, your original data is backed up in `conversation_token_usage_backup`:

```sql
-- View backup data
SELECT * FROM conversation_token_usage_backup;

-- Restore from backup (if needed)
DROP TABLE conversation_token_usage;
ALTER TABLE conversation_token_usage_backup RENAME TO conversation_token_usage;
```

### Permission Issues

Ensure your database user has the required permissions:

```sql
GRANT CREATE, DROP, ALTER, INSERT, UPDATE, DELETE, SELECT 
ON ALL TABLES IN SCHEMA public TO sdwhatsapp;

GRANT USAGE ON SCHEMA public TO sdwhatsapp;
```

## Verification

After migration, verify everything works:

1. **Run test script**: `python3 test_migration.py`
2. **Check table structure**: Verify JSONB columns exist
3. **Test application**: Ensure conversation tracking works
4. **Check indexes**: Verify GIN indexes for JSONB columns

## Rollback Plan

If you need to rollback:

1. **Stop the application**
2. **Restore from backup**:
   ```sql
   DROP TABLE conversation_token_usage;
   ALTER TABLE conversation_token_usage_backup RENAME TO conversation_token_usage;
   ```
3. **Update application code** to use old structure
4. **Restart application**

## Support

If you encounter issues:

1. Check the migration logs for detailed error messages
2. Verify database permissions and connectivity
3. Ensure all dependencies are installed
4. Run the test script to identify specific problems

The migration is designed to be safe and reversible, with comprehensive backup and verification steps.
