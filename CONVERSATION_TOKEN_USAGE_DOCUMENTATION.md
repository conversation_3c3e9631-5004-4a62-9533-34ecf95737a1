# JSONB Conversation Token Usage Tracking Implementation

## Overview

This implementation stores each conversation turn as a single row in the database using JSONB columns. Each row contains the complete context of a user-AI interaction: user input, system prompt, AI response, and token counts.

## Database Schema

### ConversationTokenUsage Table

The `conversation_token_usage` table stores detailed token usage information for each AI interaction:

```sql
CREATE TABLE conversation_token_usage (
    id VARCHAR PRIMARY KEY,
    conversation_id VARCHAR REFERENCES chatbot_conversations(id),
    tenant_id VARCHAR,
    input JSONB,                 -- User message and system prompt
    output JSONB,                -- AI-generated response
    input_tokens INTEGER DEFAULT 0,    -- Tokens in input prompt sent to LLM
    output_tokens INTEGER DEFAULT 0,   -- Tokens in response generated by LLM
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Relationships

- **ChatbotConversation** ↔ **ConversationTokenUsage**: One-to-many relationship
- Each conversation can have multiple token usage records
- Cascade delete: When a conversation is deleted, all related token usage records are deleted

## Data Structure

Each conversation turn is stored as a single row with JSONB columns containing the complete LLM interaction:

### Input JSONB Structure
```json
{
  "prompt": [
    {
      "role": "system",
      "content": "You are a helpful assistant..."
    },
    {
      "role": "user",
      "content": "User's actual message"
    },
    {
      "role": "assistant",
      "content": "Previous AI response (if any)"
    }
  ]
}
```

### Output JSONB Structure
```json
{
  "response": "Complete AI-generated response from LLM"
}
```

This approach provides complete context for each conversation turn while maintaining efficient storage and querying capabilities.

## Implementation Details

### 1. OpenAI Service Layer (`app/services/openai_service.py`)

All OpenAI API calls now return token usage information:

```python
def generate_chat_response(self, messages, max_tokens=300, temperature=None, retry_count=0):
    # ... API call ...
    return content, input_tokens, output_tokens, model

def is_off_topic(self, user_message, current_question, context=None):
    # ... API call ...
    return is_off_topic, input_tokens, output_tokens, model

def is_conversation_ending(self, user_message):
    # ... API call ...
    return is_ending, input_tokens, output_tokens, model
```

### 2. Elasticsearch Service Layer (`app/services/elasticsearch_service.py`)

Wrapper methods pass through token usage information:

```python
def generate_chat_response(self, messages, max_tokens=300, temperature=None, retry_count=0):
    return self.openai_service.generate_chat_response(messages, max_tokens, temperature, retry_count)

def is_off_topic(self, user_message, current_question, context=None):
    return self.openai_service.is_off_topic(user_message, current_question, context)
```

### 3. Chatbot Router (`app/routers/chatbot.py`)

#### Helper Function

```python
def store_conversation_turn(db: Session, conversation_id: str, tenant_id: str,
                          llm_prompt: list, llm_response: str,
                          input_tokens: int, output_tokens: int):
    """Store a complete conversation turn with full LLM prompt and response"""

    input_data = {
        "prompt": llm_prompt
    }

    output_data = {
        "response": llm_response
    }

    conversation_turn = ConversationTokenUsage(
        id=str(uuid.uuid4()),
        conversation_id=conversation_id,
        tenant_id=tenant_id,
        input=input_data,
        output=output_data,
        input_tokens=input_tokens,
        output_tokens=output_tokens
    )

    db.add(conversation_turn)
```

#### What Gets Stored

Each conversation turn (user input → AI response) creates one database row containing:
- **Complete LLM prompt** (entire message array sent to LLM)
- **Complete LLM response** (full response received from LLM)
- **Token counts** for cost tracking and analytics

## Usage Examples

### Querying Token Usage

```sql
-- Total tokens used in a conversation
SELECT
    conversation_id,
    COUNT(*) as conversation_turns,
    SUM(input_tokens) as total_input_tokens,
    SUM(output_tokens) as total_output_tokens,
    SUM(input_tokens + output_tokens) as total_tokens
FROM conversation_token_usage
WHERE conversation_id = 'your-conversation-id'
GROUP BY conversation_id;

-- Extract LLM interactions from JSONB
SELECT
    id,
    conversation_id,
    input->'prompt' as llm_prompt,
    output->>'response' as llm_response,
    input_tokens,
    output_tokens
FROM conversation_token_usage
WHERE tenant_id = 'your-tenant-id';

-- Extract specific parts of the LLM prompt
SELECT
    id,
    conversation_id,
    jsonb_array_elements(input->'prompt')->>'content' as message_content,
    jsonb_array_elements(input->'prompt')->>'role' as message_role,
    output->>'response' as llm_response
FROM conversation_token_usage
WHERE tenant_id = 'your-tenant-id';

-- Daily token usage for a tenant
SELECT
    DATE(timestamp) as date,
    COUNT(*) as conversation_turns,
    SUM(input_tokens + output_tokens) as daily_tokens
FROM conversation_token_usage
WHERE tenant_id = 'your-tenant-id'
GROUP BY DATE(timestamp)
ORDER BY date DESC;

-- Search for specific content in LLM interactions
SELECT
    conversation_id,
    input->'prompt' as llm_prompt,
    output->>'response' as llm_response,
    timestamp
FROM conversation_token_usage
WHERE output->>'response' ILIKE '%search term%'
   OR input->'prompt'::text ILIKE '%search term%';
```

### Programmatic Access

```python
from app.models import ConversationTokenUsage
from sqlalchemy.orm import Session

def get_conversation_token_usage(db: Session, conversation_id: str):
    """Get all token usage for a conversation"""
    return db.query(ConversationTokenUsage).filter(
        ConversationTokenUsage.conversation_id == conversation_id
    ).all()

def get_tenant_token_usage(db: Session, tenant_id: str, start_date=None, end_date=None):
    """Get token usage for a tenant within date range"""
    query = db.query(ConversationTokenUsage).filter(
        ConversationTokenUsage.tenant_id == tenant_id
    )
    
    if start_date:
        query = query.filter(ConversationTokenUsage.timestamp >= start_date)
    if end_date:
        query = query.filter(ConversationTokenUsage.timestamp <= end_date)
    
    return query.all()
```

## Migration

To set up the token usage tracking, run the migration:

```bash
python3 migrations/add_conversation_token_usage.py
```

This will:
- Create the `conversation_token_usage` table
- Set up proper indexes for performance
- Establish foreign key relationships

## Benefits

1. **Cost Tracking** - Monitor AI API costs per conversation/tenant
2. **Usage Analytics** - Understand conversation patterns and AI usage
3. **Performance Monitoring** - Track token efficiency and optimization opportunities
4. **Billing** - Accurate usage-based billing for tenants
5. **Debugging** - Detailed logs of all AI interactions for troubleshooting

## Performance Considerations

- Indexes on `conversation_id` and `tenant_id` for fast queries
- Cascade delete ensures data consistency
- Minimal overhead on conversation flow
- Asynchronous token counting where possible

## Future Enhancements

1. **Token Usage Alerts** - Notify when usage exceeds thresholds
2. **Usage Dashboard** - Real-time token usage visualization
3. **Cost Estimation** - Convert tokens to estimated costs
4. **Usage Optimization** - Suggest ways to reduce token consumption
5. **Batch Analytics** - Aggregate usage reports for business intelligence
