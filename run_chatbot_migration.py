#!/usr/bin/env python3
"""
Standalone migration script to add missing columns to chatbots table.
This script can be run independently without requiring the full app setup.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_connection_info():
    """Get database connection information from environment variables"""
    # Load environment variables
    load_dotenv()
    
    db_config = {
        'user': os.getenv("POSTGRES_USER", "sdwhatsapp"),
        'password': os.getenv("POSTGRES_PASSWORD", "sdwhatsapp"),
        'host': os.getenv("POSTGRES_HOST", "localhost"),
        'port': os.getenv("POSTGRES_PORT", "5432"),
        'database': os.getenv("POSTGRES_DB", "sdwhatsapp")
    }
    
    return db_config

def check_columns_exist():
    """Check if the required columns already exist in the chatbots table"""
    try:
        import psycopg2
        
        db_config = get_database_connection_info()
        
        # Connect to database
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # Check for existing columns
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'chatbots' 
            AND column_name IN ('welcome_message', 'thank_you_message', 'connected_account_id')
            ORDER BY column_name;
        """)
        
        existing_columns = [row[0] for row in cursor.fetchall()]
        
        cursor.close()
        conn.close()
        
        return existing_columns
        
    except ImportError:
        logger.error("psycopg2 module not found. Please install it with: pip install psycopg2-binary")
        return None
    except Exception as e:
        logger.error(f"Error checking columns: {str(e)}")
        return None

def run_migration():
    """Run the migration to add missing columns"""
    try:
        import psycopg2
        
        db_config = get_database_connection_info()
        logger.info(f"Connecting to database at {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # Connect to database
        conn = psycopg2.connect(**db_config)
        conn.autocommit = False
        cursor = conn.cursor()
        
        try:
            logger.info("Starting migration...")
            
            # Migration SQL statements
            migration_steps = [
                {
                    'name': 'welcome_message',
                    'sql': "ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS welcome_message VARCHAR;"
                },
                {
                    'name': 'thank_you_message', 
                    'sql': "ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS thank_you_message VARCHAR;"
                },
                {
                    'name': 'connected_account_id',
                    'sql': "ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS connected_account_id VARCHAR;"
                }
            ]
            
            # Execute each migration step
            for i, step in enumerate(migration_steps, 1):
                logger.info(f"Step {i}/3: Adding {step['name']} column...")
                cursor.execute(step['sql'])
                logger.info(f"✓ {step['name']} column added successfully")
            
            # Verify columns were added
            cursor.execute("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'chatbots' 
                AND column_name IN ('welcome_message', 'thank_you_message', 'connected_account_id')
                ORDER BY column_name;
            """)
            
            columns = cursor.fetchall()
            
            # Commit the transaction
            conn.commit()
            
            logger.info("✓ Migration completed successfully!")
            logger.info("Added columns:")
            for col in columns:
                logger.info(f"  - {col[0]} ({col[1]}, nullable: {col[2]})")
            
            return True
            
        except Exception as e:
            # Rollback on error
            conn.rollback()
            logger.error(f"Migration failed, rolling back: {str(e)}")
            raise
        finally:
            cursor.close()
            conn.close()
            
    except ImportError:
        logger.error("psycopg2 module not found. Please install it with: pip install psycopg2-binary")
        return False
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        return False

def show_sql_instructions():
    """Show instructions for running the migration manually with SQL"""
    logger.info("=" * 60)
    logger.info("MANUAL MIGRATION INSTRUCTIONS")
    logger.info("=" * 60)
    logger.info("If you cannot run this Python script, you can execute the migration manually:")
    logger.info("")
    logger.info("1. Connect to your PostgreSQL database using psql or any PostgreSQL client")
    logger.info("2. Run the SQL file: add_chatbot_columns.sql")
    logger.info("3. Or execute these SQL commands directly:")
    logger.info("")
    logger.info("   ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS welcome_message VARCHAR;")
    logger.info("   ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS thank_you_message VARCHAR;")
    logger.info("   ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS connected_account_id VARCHAR;")
    logger.info("")
    logger.info("4. Verify the columns were added:")
    logger.info("   SELECT column_name, data_type, is_nullable")
    logger.info("   FROM information_schema.columns")
    logger.info("   WHERE table_name = 'chatbots'")
    logger.info("   AND column_name IN ('welcome_message', 'thank_you_message', 'connected_account_id');")
    logger.info("")
    logger.info("=" * 60)

def main():
    """Main function to run the migration"""
    logger.info("Chatbot Table Migration Script")
    logger.info("=" * 40)
    
    # Check if columns already exist
    logger.info("Checking current table structure...")
    existing_columns = check_columns_exist()
    
    if existing_columns is None:
        logger.warning("Could not check existing columns. Proceeding with migration...")
    elif len(existing_columns) == 3:
        logger.info("✓ All required columns already exist:")
        for col in existing_columns:
            logger.info(f"  - {col}")
        logger.info("Migration not needed.")
        return
    elif len(existing_columns) > 0:
        logger.info(f"Found {len(existing_columns)}/3 columns: {existing_columns}")
        logger.info("Proceeding with migration to add missing columns...")
    else:
        logger.info("No target columns found. Running full migration...")
    
    # Run the migration
    success = run_migration()
    
    if not success:
        logger.error("Migration failed. See instructions below for manual migration:")
        show_sql_instructions()
        sys.exit(1)
    else:
        logger.info("✓ Migration completed successfully!")
        logger.info("The chatbots table now has all required columns:")
        logger.info("  - welcome_message")
        logger.info("  - thank_you_message") 
        logger.info("  - connected_account_id")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Migration cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)
