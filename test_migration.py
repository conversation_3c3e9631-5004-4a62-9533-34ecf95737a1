#!/usr/bin/env python3
"""
Test script to verify the conversation_token_usage migration works correctly
"""

import os
import sys
import uuid
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection
DB_USER = os.getenv("POSTGRES_USER", "sdwhatsapp")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "sdwhatsapp")
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = "5432"
DB_NAME = os.getenv("POSTGRES_DB", "sdwhatsapp")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def test_table_structure():
    """Test that the table has the correct structure"""
    print("Testing table structure...")
    
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as connection:
            # Check table exists
            check_table = text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name = 'conversation_token_usage'
            """)
            result = connection.execute(check_table)
            
            if result.rowcount == 0:
                print("❌ Table conversation_token_usage does not exist")
                return False
            
            print("✅ Table exists")
            
            # Check columns
            check_columns = text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'conversation_token_usage'
                ORDER BY ordinal_position
            """)
            result = connection.execute(check_columns)
            columns = {row[0]: row[1] for row in result.fetchall()}
            
            required_columns = {
                'id': 'character varying',
                'conversation_id': 'character varying',
                'tenant_id': 'character varying',
                'input': 'jsonb',
                'output': 'jsonb',
                'input_tokens': 'integer',
                'output_tokens': 'integer',
                'timestamp': 'timestamp without time zone'
            }
            
            for col_name, expected_type in required_columns.items():
                if col_name not in columns:
                    print(f"❌ Missing column: {col_name}")
                    return False
                
                actual_type = columns[col_name]
                if 'json' in expected_type and 'json' in actual_type:
                    print(f"✅ Column {col_name} has JSON type: {actual_type}")
                elif expected_type in actual_type or actual_type in expected_type:
                    print(f"✅ Column {col_name} has correct type: {actual_type}")
                else:
                    print(f"⚠️  Column {col_name} type mismatch. Expected: {expected_type}, Got: {actual_type}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error testing table structure: {str(e)}")
        return False

def test_data_insertion():
    """Test inserting and retrieving JSONB data"""
    print("\nTesting data insertion and retrieval...")
    
    try:
        engine = create_engine(DATABASE_URL)
        with engine.begin() as connection:
            # Test data
            test_id = str(uuid.uuid4())
            test_conversation_id = str(uuid.uuid4())
            test_tenant_id = "test_tenant"
            
            input_data = {
                "user": "What are your business hours?",
                "system": "You are a helpful assistant. Answer questions about business operations."
            }
            
            output_data = {
                "assistant": "Our business hours are Monday to Friday, 9 AM to 5 PM."
            }
            
            # Insert test data
            insert_query = text("""
                INSERT INTO conversation_token_usage 
                (id, conversation_id, tenant_id, input, output, input_tokens, output_tokens, timestamp)
                VALUES (:id, :conversation_id, :tenant_id, :input, :output, :input_tokens, :output_tokens, :timestamp)
            """)
            
            connection.execute(insert_query, {
                'id': test_id,
                'conversation_id': test_conversation_id,
                'tenant_id': test_tenant_id,
                'input': input_data,
                'output': output_data,
                'input_tokens': 25,
                'output_tokens': 15,
                'timestamp': datetime.utcnow()
            })
            
            print("✅ Data inserted successfully")
            
            # Retrieve and verify data
            select_query = text("""
                SELECT id, conversation_id, tenant_id, input, output, input_tokens, output_tokens
                FROM conversation_token_usage 
                WHERE id = :id
            """)
            
            result = connection.execute(select_query, {'id': test_id})
            row = result.fetchone()
            
            if not row:
                print("❌ Failed to retrieve inserted data")
                return False
            
            print("✅ Data retrieved successfully")
            
            # Verify JSONB data
            retrieved_input = row[3]  # input column
            retrieved_output = row[4]  # output column
            
            if retrieved_input['user'] == input_data['user']:
                print("✅ Input JSONB user field correct")
            else:
                print("❌ Input JSONB user field incorrect")
                return False
            
            if retrieved_input['system'] == input_data['system']:
                print("✅ Input JSONB system field correct")
            else:
                print("❌ Input JSONB system field incorrect")
                return False
            
            if retrieved_output['assistant'] == output_data['assistant']:
                print("✅ Output JSONB assistant field correct")
            else:
                print("❌ Output JSONB assistant field incorrect")
                return False
            
            # Test JSONB queries
            jsonb_query = text("""
                SELECT input->>'user' as user_message, 
                       output->>'assistant' as ai_response,
                       input_tokens, output_tokens
                FROM conversation_token_usage 
                WHERE input->>'user' = :user_message
            """)
            
            result = connection.execute(jsonb_query, {'user_message': input_data['user']})
            jsonb_row = result.fetchone()
            
            if jsonb_row and jsonb_row[0] == input_data['user']:
                print("✅ JSONB query with ->> operator works")
            else:
                print("❌ JSONB query failed")
                return False
            
            # Clean up test data
            delete_query = text("DELETE FROM conversation_token_usage WHERE id = :id")
            connection.execute(delete_query, {'id': test_id})
            print("✅ Test data cleaned up")
            
            return True
            
    except Exception as e:
        print(f"❌ Error testing data insertion: {str(e)}")
        return False

def test_indexes():
    """Test that indexes are created correctly"""
    print("\nTesting indexes...")
    
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as connection:
            # Check indexes
            check_indexes = text("""
                SELECT indexname, indexdef
                FROM pg_indexes 
                WHERE tablename = 'conversation_token_usage'
            """)
            result = connection.execute(check_indexes)
            indexes = result.fetchall()
            
            index_names = [idx[0] for idx in indexes]
            
            expected_indexes = [
                'idx_conversation_token_usage_conversation_id',
                'idx_conversation_token_usage_tenant_id',
                'idx_conversation_token_usage_input_gin',
                'idx_conversation_token_usage_output_gin'
            ]
            
            for expected_idx in expected_indexes:
                if any(expected_idx in idx_name for idx_name in index_names):
                    print(f"✅ Index found: {expected_idx}")
                else:
                    print(f"⚠️  Index not found: {expected_idx}")
            
            # Check for GIN indexes on JSONB columns
            gin_indexes = [idx for idx in indexes if 'gin' in idx[1].lower()]
            if len(gin_indexes) >= 2:
                print("✅ GIN indexes found for JSONB columns")
            else:
                print("⚠️  GIN indexes for JSONB columns may be missing")
            
            return True
            
    except Exception as e:
        print(f"❌ Error testing indexes: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("CONVERSATION TOKEN USAGE MIGRATION TEST")
    print("=" * 60)
    
    tests = [
        ("Table Structure", test_table_structure),
        ("Data Insertion/Retrieval", test_data_insertion),
        ("Indexes", test_indexes)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("\nYour conversation_token_usage table is ready for use with JSONB structure.")
        print("\nExample usage:")
        print("- Store: input={'user': 'message', 'system': 'prompt'}")
        print("- Store: output={'assistant': 'response'}")
        print("- Query: SELECT input->>'user' FROM conversation_token_usage")
        return True
    else:
        print("\n❌ SOME TESTS FAILED. Please check the migration.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        sys.exit(1)
