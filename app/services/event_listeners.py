import asyncio
import logging
from typing import Dict, Any
from aio_pika.abc import AbstractIncomingMessage
from app.services.rabbitmq_service import rabbitmq_service
from app.database import get_db
from app.models import Chatbot
from sqlalchemy import func

logger = logging.getLogger(__name__)


class SchedulerEventListener:
    """
    Event listener for scheduler-related events
    """
    
    def __init__(self):
        self.is_running = False
        self.listener_task = None
    
    async def start(self):
        """
        Start the scheduler event listener
        """
        if self.is_running:
            logger.warning("Scheduler event listener is already running")
            return
        
        try:
            logger.info("Starting scheduler event listener...")
            
            # Connect to RabbitMQ
            connected = await rabbitmq_service.connect()
            if not connected:
                raise RuntimeError("Failed to connect to RabbitMQ")
            
            # Setup the scheduler listener
            await rabbitmq_service.setup_scheduler_listener()
            
            self.is_running = True
            logger.info("Scheduler event listener started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start scheduler event listener: {str(e)}")
            self.is_running = False
            raise
    
    async def stop(self):
        """
        Stop the scheduler event listener
        """
        if not self.is_running:
            logger.warning("Scheduler event listener is not running")
            return
        
        try:
            logger.info("Stopping scheduler event listener...")
            
            # Disconnect from RabbitMQ
            await rabbitmq_service.disconnect()
            
            self.is_running = False
            logger.info("Scheduler event listener stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping scheduler event listener: {str(e)}")
    
    async def handle_collect_usage_event(self, payload: Dict[str, Any], message: AbstractIncomingMessage):
        """
        Handle the scheduler.collect.usage event
        
        Args:
            payload: Event payload (expected to be empty {} for this event)
            message: RabbitMQ message object
        """
        try:
            logger.info("Processing scheduler.collect.usage event")
            logger.debug(f"Message routing key: {message.routing_key}")
            logger.debug(f"Message payload: {payload}")
            
            # Since the event has an empty payload {}, we just acknowledge it
            # This is where you would implement any usage collection logic
            
            # Example usage collection tasks that could be triggered:
            # 1. Collect conversation token usage statistics
            # 2. Generate usage reports
            # 3. Clean up old conversation data
            # 4. Update usage metrics
            # 5. Send usage data to analytics systems
            
            await self._perform_usage_collection()
            
            logger.info("Successfully processed scheduler.collect.usage event")
            
        except Exception as e:
            logger.error(f"Error processing scheduler.collect.usage event: {str(e)}")
            raise
    
    async def _perform_usage_collection(self):
        """
        Perform actual usage collection tasks
        This is a placeholder for the actual implementation
        """
        try:
            logger.info("Performing usage collection tasks...")
            
            db = next(get_db())
            
            # Get total non-Draft status chatbots by tenantId
            usage_data = (
                db.query(Chatbot.tenant_id, func.count(Chatbot.id))
                .filter(Chatbot.status != "DRAFT")
                .group_by(Chatbot.tenant_id)
                .all()
            )
            
            # Format the payload
            payload = [
                {
                    "tenantId": tenant_id,
                    "usageEntity": "CHATBOT",
                    "count": count
                }
                for tenant_id, count in usage_data
            ]
            
            if payload:
                # Publish the event
                await rabbitmq_service.publish_event(
                    "usage.collected",
                    payload
                )
                logger.info(f"Published usage collection event with payload: {payload}")
            else:
                logger.info("No usage data to publish.")

            logger.info("Usage collection tasks completed.")
            
        except Exception as e:
            logger.error(f"Error performing usage collection: {str(e)}")
            raise
        finally:
            db.close()
    
    async def _cleanup_old_conversations(self):
        """
        Clean up old conversation data
        Placeholder for actual implementation
        """
        logger.info("Cleaning up old conversations (placeholder)")
        # Implement cleanup logic here
        pass
    
    async def _collect_token_usage_stats(self):
        """
        Collect token usage statistics
        Placeholder for actual implementation
        """
        logger.info("Collecting token usage statistics (placeholder)")
        # Implement statistics collection here
        pass
    
    async def _generate_usage_reports(self):
        """
        Generate usage reports
        Placeholder for actual implementation
        """
        logger.info("Generating usage reports (placeholder)")
        # Implement report generation here
        pass
    
    async def _update_usage_metrics(self):
        """
        Update usage metrics
        Placeholder for actual implementation
        """
        logger.info("Updating usage metrics (placeholder)")
        # Implement metrics update here
        pass


class EventListenerManager:
    """
    Manager for all event listeners
    """
    
    def __init__(self):
        self.scheduler_listener = SchedulerEventListener()
        self.listeners = [self.scheduler_listener]
    
    async def start_all(self):
        """
        Start all event listeners
        """
        logger.info("Starting all event listeners...")
        
        for listener in self.listeners:
            try:
                await listener.start()
            except Exception as e:
                logger.error(f"Failed to start listener {listener.__class__.__name__}: {str(e)}")
                # Continue starting other listeners even if one fails
        
        logger.info("Event listener startup completed")
    
    async def stop_all(self):
        """
        Stop all event listeners
        """
        logger.info("Stopping all event listeners...")
        
        for listener in self.listeners:
            try:
                await listener.stop()
            except Exception as e:
                logger.error(f"Error stopping listener {listener.__class__.__name__}: {str(e)}")
        
        logger.info("All event listeners stopped")


# Global event listener manager instance
event_listener_manager = EventListenerManager()
