import os
import json
import redis
from dotenv import load_dotenv
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class RedisService:
    def __init__(self):
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        logger.info("Connecting to Red<PERSON>", extra={"redis_url": redis_url})
        
        self.redis_client = redis.from_url(redis_url)
        
        try:
            # Test connection
            self.redis_client.ping()
            logger.info("Connected to Redis successfully")
        except Exception as e:
            logger.error("Failed to connect to Red<PERSON>", exc_info=True, extra={"error": str(e)})
            raise
    
    def store_conversation_state(self, conversation_id, state, expire_seconds=3600):
        """Store conversation state in Redis with expiration"""
        try:
            key = f"conversation:{conversation_id}"
            
            # Ensure state is serializable
            json_state = json.dumps(state)
            
            # Store in Redis
            self.redis_client.set(key, json_state, ex=expire_seconds)
            
            logger.info("Stored conversation state", extra={
                "conversation_id": conversation_id, 
                "expire_seconds": expire_seconds,
                "state_size": len(json_state)
            })
            
            return True
        except Exception as e:
            logger.error(f"Error storing conversation state: {str(e)}", exc_info=True, extra={
                "conversation_id": conversation_id
            })
            return False
    
    def get_conversation_state(self, conversation_id):
        """Retrieve conversation state from Redis"""
        try:
            key = f"conversation:{conversation_id}"
            data = self.redis_client.get(key)
            
            if data:
                state = json.loads(data)
                logger.info(f"Retrieved conversation state", extra={
                    "conversation_id": conversation_id,
                    "state_keys": list(state.keys()) if isinstance(state, dict) else "Not a dict"
                })
                return state
            
            logger.warning(f"No state found for conversation {conversation_id}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from Redis: {str(e)}", exc_info=True, extra={
                "conversation_id": conversation_id,
                "raw_data": data[:100] if data else "None"
            })
            return None
        except Exception as e:
            logger.error(f"Error retrieving conversation state: {str(e)}", exc_info=True, extra={
                "conversation_id": conversation_id
            })
            return None
    
    def update_conversation_ttl(self, conversation_id, expire_seconds=3600):
        """Update the TTL for a conversation"""
        key = f"conversation:{conversation_id}"
        self.redis_client.expire(key, expire_seconds)
        logger.info(f"Updated TTL for conversation {conversation_id}")