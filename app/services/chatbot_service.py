import logging
import uuid
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import HTT<PERSON>Exception
from app.database import get_db
from app.models import (
    Chatbot,
    ChatbotQuestion,
    ChatbotKnowledgebase,
    ChatbotConversation,
    Document,
    ChatbotCreate,
    Chatbot<PERSON>pdate,
    QuestionCreate
)
from app.services.rabbitmq_service import rabbitmq_service

logger = logging.getLogger(__name__)


class ChatbotService:
    """
    Service class for chatbot-related operations including usage collection
    """
    
    def __init__(self):
        """
        Initialize the ChatbotService
        """
        logger.info("Initialized ChatbotService")
    
    async def collect_and_publish_chatbot_usage(self):
        """
        Collect chatbot usage data and publish it to RabbitMQ
        
        This method:
        1. Queries the database for non-DRAFT chatbots grouped by tenant
        2. Formats the data for usage reporting
        3. Publishes the usage data to the RabbitMQ exchange
        """
        db = None
        try:
            logger.info("Starting chatbot usage collection...")
            
            db = next(get_db())
            
            # Get total non-Draft status chatbots by tenantId
            usage_data = (
                db.query(Chatbot.tenant_id, func.count(Chatbot.id))
                .filter(Chatbot.status != "DRAFT")
                .group_by(Chatbot.tenant_id)
                .all()
            )
            
            # Format the payload for usage reporting
            payload = [
                {
                    "tenantId": tenant_id,
                    "usageEntity": "CHATBOT",
                    "count": count
                }
                for tenant_id, count in usage_data
            ]
            
            # Publish the usage data if there's any data to publish
            if payload:
                await rabbitmq_service.publish_event(
                    "usage.collected",
                    payload
                )
                logger.info(f"Published chatbot usage data for {len(payload)} tenants: {payload}")
            else:
                logger.info("No chatbot usage data to publish.")
            
            logger.info("Chatbot usage collection completed successfully")
            
        except Exception as e:
            logger.error(f"Error collecting chatbot usage: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_count_by_tenant(self, tenant_id: str, include_draft: bool = False) -> int:
        """
        Get the count of chatbots for a specific tenant
        
        Args:
            tenant_id: The tenant ID to get chatbot count for
            include_draft: Whether to include DRAFT status chatbots in the count
            
        Returns:
            int: Number of chatbots for the tenant
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(func.count(Chatbot.id)).filter(Chatbot.tenant_id == tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            count = query.scalar()
            
            logger.info(f"Chatbot count for tenant {tenant_id}: {count} (include_draft: {include_draft})")
            return count
            
        except Exception as e:
            logger.error(f"Error getting chatbot count for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_all_tenant_chatbot_counts(self, include_draft: bool = False) -> List[Dict[str, Any]]:
        """
        Get chatbot counts for all tenants
        
        Args:
            include_draft: Whether to include DRAFT status chatbots in the count
            
        Returns:
            List[Dict]: List of dictionaries containing tenant_id and count
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(Chatbot.tenant_id, func.count(Chatbot.id)).group_by(Chatbot.tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            results = query.all()
            
            tenant_counts = [
                {
                    "tenant_id": tenant_id,
                    "count": count
                }
                for tenant_id, count in results
            ]
            
            logger.info(f"Retrieved chatbot counts for {len(tenant_counts)} tenants (include_draft: {include_draft})")
            return tenant_counts
            
        except Exception as e:
            logger.error(f"Error getting chatbot counts for all tenants: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_by_id(self, chatbot_id: str, tenant_id: str) -> Optional[Chatbot]:
        """
        Get a chatbot by ID and tenant ID
        
        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID
            
        Returns:
            Optional[Chatbot]: The chatbot if found, None otherwise
        """
        db = None
        try:
            db = next(get_db())
            
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()
            
            if chatbot:
                logger.info(f"Found chatbot {chatbot_id} for tenant {tenant_id}")
            else:
                logger.warning(f"Chatbot {chatbot_id} not found for tenant {tenant_id}")
            
            return chatbot
            
        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id} for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbots_by_tenant(self, tenant_id: str, include_draft: bool = True) -> List[Chatbot]:
        """
        Get all chatbots for a specific tenant
        
        Args:
            tenant_id: The tenant ID
            include_draft: Whether to include DRAFT status chatbots
            
        Returns:
            List[Chatbot]: List of chatbots for the tenant
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(Chatbot).filter(Chatbot.tenant_id == tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            chatbots = query.all()
            
            logger.info(f"Found {len(chatbots)} chatbots for tenant {tenant_id} (include_draft: {include_draft})")
            return chatbots
            
        except Exception as e:
            logger.error(f"Error getting chatbots for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_statistics(self, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get comprehensive chatbot statistics
        
        Args:
            tenant_id: Optional tenant ID to filter statistics for a specific tenant
            
        Returns:
            Dict[str, Any]: Dictionary containing various chatbot statistics
        """
        db = None
        try:
            db = next(get_db())
            
            stats = {}
            
            if tenant_id:
                # Statistics for a specific tenant
                total_query = db.query(func.count(Chatbot.id)).filter(Chatbot.tenant_id == tenant_id)
                active_query = db.query(func.count(Chatbot.id)).filter(
                    Chatbot.tenant_id == tenant_id,
                    Chatbot.status == "ACTIVE"
                )
                draft_query = db.query(func.count(Chatbot.id)).filter(
                    Chatbot.tenant_id == tenant_id,
                    Chatbot.status == "DRAFT"
                )
                
                stats = {
                    "tenant_id": tenant_id,
                    "total_chatbots": total_query.scalar(),
                    "active_chatbots": active_query.scalar(),
                    "draft_chatbots": draft_query.scalar()
                }
            else:
                # Global statistics
                total_chatbots = db.query(func.count(Chatbot.id)).scalar()
                active_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "ACTIVE").scalar()
                draft_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "DRAFT").scalar()
                inactive_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "INACTIVE").scalar()
                
                # Count by tenant
                tenant_counts = db.query(Chatbot.tenant_id, func.count(Chatbot.id)).group_by(Chatbot.tenant_id).all()
                
                stats = {
                    "total_chatbots": total_chatbots,
                    "active_chatbots": active_chatbots,
                    "draft_chatbots": draft_chatbots,
                    "inactive_chatbots": inactive_chatbots,
                    "total_tenants": len(tenant_counts),
                    "tenant_breakdown": [
                        {"tenant_id": tenant_id, "count": count}
                        for tenant_id, count in tenant_counts
                    ]
                }
            
            logger.info(f"Generated chatbot statistics: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error getting chatbot statistics: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    # CRUD Operations

    def create_chatbot(self, chatbot_data: ChatbotCreate, tenant_id: str) -> Dict[str, Any]:
        """
        Create a new chatbot

        Args:
            chatbot_data: ChatbotCreate model with chatbot information
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Created chatbot information
        """
        db = None
        try:
            db = next(get_db())

            # Validate type value
            chatbot_type = chatbot_data.type.upper()
            if chatbot_type not in ["AI", "RULE"]:
                raise HTTPException(status_code=400, detail="Chatbot type must be either 'AI' or 'RULE'")

            # Generate unique chatbot ID
            chatbot_id = str(uuid.uuid4())

            # Create chatbot with minimal information
            chatbot = Chatbot(
                id=chatbot_id,
                tenant_id=tenant_id,
                name=chatbot_data.name,
                type=chatbot_type,
                description=chatbot_data.description or "",
                welcome_message=chatbot_data.welcome_message,
                thank_you_message=chatbot_data.thank_you_message,
                connected_account_id=chatbot_data.connected_account_id,
                status="DRAFT"  # Initial status is DRAFT until fully configured
            )

            # Add chatbot to database
            db.add(chatbot)
            db.commit()
            db.refresh(chatbot)

            logger.info(f"Created chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "type": chatbot.type,
                "description": chatbot.description,
                "welcome_message": chatbot.welcome_message,
                "thank_you_message": chatbot.thank_you_message,
                "connected_account_id": chatbot.connected_account_id,
                "status": chatbot.status,
                "created_at": chatbot.created_at
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error creating chatbot: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error creating chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def update_chatbot(self, chatbot_id: str, chatbot_data: ChatbotUpdate, tenant_id: str) -> Dict[str, Any]:
        """
        Update an existing chatbot

        Args:
            chatbot_id: The chatbot ID to update
            chatbot_data: ChatbotUpdate model with updated information
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Updated chatbot information
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Validate knowledgebase IDs if provided
            if chatbot_data.knowledgebase_ids is not None:
                for kb_id in chatbot_data.knowledgebase_ids:
                    kb = db.query(Document).filter(
                        Document.id == kb_id,
                        Document.tenant_id == tenant_id
                    ).first()
                    if not kb:
                        raise HTTPException(status_code=404, detail=f"Knowledgebase with ID {kb_id} not found")

            # Update chatbot fields if provided
            if chatbot_data.name is not None:
                chatbot.name = chatbot_data.name

            if chatbot_data.description is not None:
                chatbot.description = chatbot_data.description

            if chatbot_data.welcome_message is not None:
                chatbot.welcome_message = chatbot_data.welcome_message

            if chatbot_data.thank_you_message is not None:
                chatbot.thank_you_message = chatbot_data.thank_you_message

            if chatbot_data.connected_account_id is not None:
                chatbot.connected_account_id = chatbot_data.connected_account_id

            # Update questions if provided
            if chatbot_data.questions is not None:
                # Delete existing questions
                db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot_id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).delete()

                # Add new questions
                for q_data in chatbot_data.questions:
                    question = ChatbotQuestion(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        tenant_id=tenant_id,
                        question=q_data.question
                    )
                    db.add(question)

            # Update knowledgebase associations if provided
            if chatbot_data.knowledgebase_ids is not None:
                # Delete existing associations
                db.query(ChatbotKnowledgebase).filter(
                    ChatbotKnowledgebase.chatbot_id == chatbot_id,
                    ChatbotKnowledgebase.tenant_id == tenant_id
                ).delete()

                # Add new associations
                for kb_id in chatbot_data.knowledgebase_ids:
                    kb_assoc = ChatbotKnowledgebase(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        document_id=kb_id,
                        tenant_id=tenant_id
                    )
                    db.add(kb_assoc)

            # Commit changes
            db.commit()
            db.refresh(chatbot)

            # Fetch questions for response
            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            # Fetch knowledgebase IDs for response
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            logger.info(f"Updated chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "description": chatbot.description,
                "welcome_message": chatbot.welcome_message,
                "thank_you_message": chatbot.thank_you_message,
                "connected_account_id": chatbot.connected_account_id,
                "questions": [{"id": q.id, "question": q.question} for q in questions],
                "knowledgebase_ids": [kb.document_id for kb in kb_assocs],
                "updated_at": chatbot.updated_at
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error updating chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def delete_chatbot(self, chatbot_id: str, tenant_id: str) -> Dict[str, Any]:
        """
        Delete a chatbot and all its associated data

        Args:
            chatbot_id: The chatbot ID to delete
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Deletion confirmation message
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Delete questions
            db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).delete()

            # Delete knowledgebase associations
            db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).delete()

            # Delete chatbot
            db.delete(chatbot)
            db.commit()

            logger.info(f"Deleted chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Chatbot deleted successfully",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error deleting chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error deleting chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def get_chatbot_with_details(self, chatbot_id: str, tenant_id: str) -> Dict[str, Any]:
        """
        Get a chatbot with all its details including questions and knowledgebase associations

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Chatbot details
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Fetch questions for response
            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            # Fetch knowledgebase IDs for response
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            logger.info(f"Retrieved chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "type": chatbot.type,
                "description": chatbot.description,
                "welcome_message": chatbot.welcome_message,
                "thank_you_message": chatbot.thank_you_message,
                "connected_account_id": chatbot.connected_account_id,
                "status": chatbot.status,
                "questions": [{"id": q.id, "question": q.question} for q in questions],
                "knowledgebase_ids": [kb.document_id for kb in kb_assocs],
                "created_at": chatbot.created_at,
                "updated_at": chatbot.updated_at
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error getting chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def delete_question(self, chatbot_id: str, question_id: str, tenant_id: str) -> Dict[str, Any]:
        """
        Delete a specific question from a chatbot

        Args:
            chatbot_id: The chatbot ID
            question_id: The question ID to delete
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Deletion confirmation message
        """
        db = None
        try:
            db = next(get_db())

            # Check if question exists
            question = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.id == question_id,
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).first()

            if not question:
                raise HTTPException(status_code=404, detail="Question not found")

            # Delete question
            db.delete(question)
            db.commit()

            logger.info(f"Deleted question {question_id} from chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Question deleted successfully",
                "question_id": question_id,
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error deleting question {question_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error deleting question: {str(e)}")
        finally:
            if db:
                db.close()

    def configure_chatbot_questions(self, chatbot_id: str, questions: List[QuestionCreate], tenant_id: str) -> Dict[str, Any]:
        """
        Configure questions for a chatbot (replace all existing questions)

        Args:
            chatbot_id: The chatbot ID
            questions: List of QuestionCreate objects
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Configuration result with questions
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Delete existing questions
            db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).delete()

            # Add new questions
            created_questions = []
            for q_data in questions:
                question = ChatbotQuestion(
                    id=str(uuid.uuid4()),
                    chatbot_id=chatbot_id,
                    tenant_id=tenant_id,
                    question=q_data.question
                )
                db.add(question)
                created_questions.append(question)

            # Update chatbot status to ACTIVE if it was DRAFT
            if chatbot.status == "DRAFT":
                chatbot.status = "ACTIVE"

            db.commit()

            # Refresh to get the created questions with their IDs
            for question in created_questions:
                db.refresh(question)

            logger.info(f"Configured {len(questions)} questions for chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Questions configured successfully",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id,
                "questions": [{"id": q.id, "question": q.question} for q in created_questions],
                "status": chatbot.status
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error configuring questions for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error configuring questions: {str(e)}")
        finally:
            if db:
                db.close()

    def list_chatbots(self, tenant_id: str, include_draft: bool = True) -> List[Dict[str, Any]]:
        """
        List all chatbots for a tenant

        Args:
            tenant_id: The tenant ID
            include_draft: Whether to include DRAFT status chatbots

        Returns:
            List[Dict[str, Any]]: List of chatbots with basic information
        """
        db = None
        try:
            db = next(get_db())

            query = db.query(Chatbot).filter(Chatbot.tenant_id == tenant_id)

            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")

            chatbots = query.all()

            result = []
            for chatbot in chatbots:
                # Get question count for each chatbot
                question_count = db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot.id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).count()

                # Get knowledgebase count for each chatbot
                kb_count = db.query(ChatbotKnowledgebase).filter(
                    ChatbotKnowledgebase.chatbot_id == chatbot.id,
                    ChatbotKnowledgebase.tenant_id == tenant_id
                ).count()

                result.append({
                    "id": chatbot.id,
                    "tenant_id": chatbot.tenant_id,
                    "name": chatbot.name,
                    "type": chatbot.type,
                    "description": chatbot.description,
                    "status": chatbot.status,
                    "question_count": question_count,
                    "knowledgebase_count": kb_count,
                    "created_at": chatbot.created_at,
                    "updated_at": chatbot.updated_at
                })

            logger.info(f"Listed {len(result)} chatbots for tenant {tenant_id} (include_draft: {include_draft})")

            return result

        except Exception as e:
            logger.error(f"Error listing chatbots for tenant {tenant_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error listing chatbots: {str(e)}")
        finally:
            if db:
                db.close()

    def get_chatbot_for_conversation(self, chatbot_id: str, tenant_id: str) -> Optional[Chatbot]:
        """
        Get a chatbot for conversation purposes (used by conversation endpoints)

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Optional[Chatbot]: The chatbot if found, None otherwise
        """
        db = None
        try:
            db = next(get_db())

            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if chatbot:
                logger.info(f"Retrieved chatbot {chatbot_id} for conversation")
            else:
                logger.warning(f"Chatbot {chatbot_id} not found for tenant {tenant_id}")

            return chatbot

        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id} for conversation: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def get_chatbot_questions_for_conversation(self, chatbot_id: str, tenant_id: str) -> List[ChatbotQuestion]:
        """
        Get chatbot questions for conversation purposes

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            List[ChatbotQuestion]: List of questions for the chatbot
        """
        db = None
        try:
            db = next(get_db())

            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            logger.info(f"Retrieved {len(questions)} questions for chatbot {chatbot_id}")

            return questions

        except Exception as e:
            logger.error(f"Error getting questions for chatbot {chatbot_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
