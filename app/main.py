import os
from dotenv import load_dotenv

# Load environment variables at the very beginning
load_dotenv(verbose=True)

# Log the loaded environment variables (without secrets)
print("Environment variables loaded:")
print(f"AWS_ACCESS_KEY_ID present: {os.getenv('AWS_ACCESS_KEY_ID') is not None}")
print(f"AWS_SECRET_ACCESS_KEY present: {os.getenv('AWS_SECRET_ACCESS_KEY') is not None}")
print(f"AWS_REGION: {os.getenv('AWS_REGION')}")
print(f"AWS_S3_BUCKET_NAME: {os.getenv('AWS_S3_BUCKET_NAME')}")

from fastapi import FastAPI, Depends, Request, Response, Security
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from fastapi.openapi.models import SecuritySchemeType, SecurityScheme
from app.routers import chatbot, documents  # Add documents import here
from app.database import engine, Base
from app.dependencies import get_auth_context, AuthContext, security
from fastapi.security import HTTPAuthorizationCredentials
from prometheus_client import make_asgi_app
from app.monitoring import REQUEST_COUNT, LatencyTimer, ACTIVE_CONNECTIONS, AUTH_FAILURE_COUNT
import time
import logging
from app.logging_config import setup_logging
import uuid
from fastapi.responses import RedirectResponse
from app.services.event_listeners import event_listener_manager

# Set up logging
log_level = os.getenv("LOG_LEVEL", "INFO")
logger = setup_logging(
    log_level=getattr(logging, log_level)
)

# Create database tables
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Knowledge Base API",
    description="API for managing knowledge base and chatbot interactions",
    version="1.0.0",
    openapi_tags=[
        {"name": "chatbot", "description": "Chatbot operations"},
        {"name": "documents", "description": "Document management operations"}
    ],
    swagger_ui_parameters={
        "persistAuthorization": True,
        "displayRequestDuration": True,
        "filter": True,
        "tryItOutEnabled": True
    },
    openapi_url="/api/swagger.json",  # Customize the OpenAPI JSON URL
)

# Override the default OpenAPI schema to add security scheme
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Add security scheme
    openapi_schema["components"] = openapi_schema.get("components", {})
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Enter JWT token"
        }
    }
    
    # Add global security requirement
    openapi_schema["security"] = [{"BearerAuth": []}]
    
    # Add additional metadata if needed
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

# Create metrics endpoint
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify the allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add request logging middleware
@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    start_time = time.time()
    
    # Generate request ID
    request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
    
    # Add request ID to request state
    request.state.request_id = request_id
    
    # Log request
    logger.info(
        f"Request started",
        extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "client_ip": request.client.host,
            "user_agent": request.headers.get("User-Agent", "Unknown")
        }
    )
    
    # Process request
    try:
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            f"Request completed",
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "process_time_ms": round(process_time * 1000, 2)
            }
        )
        
        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        
        return response
    except Exception as e:
        # Log exception
        logger.error(
            f"Request failed: {str(e)}",
            exc_info=True,
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path
            }
        )
        raise

# Add Prometheus middleware
@app.middleware("http")
async def prometheus_middleware(request: Request, call_next):
    # Skip metrics endpoint to avoid recursion
    if request.url.path == "/metrics":
        return await call_next(request)
    
    # Increment active connections
    ACTIVE_CONNECTIONS.inc()
    
    # Track request latency
    method = request.method
    endpoint = request.url.path
    
    with LatencyTimer(method, endpoint):
        # Process the request
        response = await call_next(request)
    
    # Record request count
    REQUEST_COUNT.labels(
        method=method,
        endpoint=endpoint,
        status_code=response.status_code
    ).inc()
    
    # Decrement active connections
    ACTIVE_CONNECTIONS.dec()
    
    return response

# Add authentication middleware
@app.middleware("http")
async def auth_middleware(request: Request, call_next):
    # Skip authentication for certain paths
    if (request.url.path == "/" or 
        request.url.path.startswith("/docs") or 
        request.url.path.startswith("/openapi") or
        request.url.path == "/api/swagger.json" or
        request.url.path.startswith("/redoc") or
        request.url.path.startswith("/static") or
        request.url.path == "/metrics/"):  # Skip authentication for metrics endpoint
        return await call_next(request)
    
    # Get Authorization header
    authorization = request.headers.get("Authorization")
    if not authorization:
        AUTH_FAILURE_COUNT.labels(reason="missing_header").inc()
        logger.warning(
            "Authentication failed: Missing Authorization header",
            extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "path": request.url.path,
                "client_ip": request.client.host
            }
        )
        return Response(
            content='{"detail":"Authorization header missing"}',
            status_code=401,
            media_type="application/json",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    try:
        # Extract token from Authorization header
        if authorization.startswith("Bearer "):
            token = authorization.replace("Bearer ", "")
        else:
            AUTH_FAILURE_COUNT.labels(reason="invalid_scheme").inc()
            logger.warning(
                "Authentication failed: Invalid scheme",
                extra={
                    "request_id": getattr(request.state, "request_id", "unknown"),
                    "path": request.url.path,
                    "client_ip": request.client.host
                }
            )
            return Response(
                content='{"detail":"Invalid authentication scheme. Expected Bearer"}',
                status_code=401,
                media_type="application/json",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Create credentials object
        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials=token)
        
        # Process the token and store auth context in request.state
        auth_context = await get_auth_context(credentials, request)
        
        # Log successful authentication
        logger.info(
            "Authentication successful",
            extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "user_id": auth_context.user_id,
                "tenant_id": auth_context.tenant_id
            }
        )
        
        # Continue with the request
        return await call_next(request)
    except Exception as e:
        # Handle authentication errors
        AUTH_FAILURE_COUNT.labels(reason="auth_error").inc()
        logger.error(
            f"Authentication error: {str(e)}",
            exc_info=True,
            extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "path": request.url.path,
                "client_ip": request.client.host
            }
        )
        return Response(
            content=f'{{"detail":"Authentication error: {str(e)}"}}',
            status_code=401,
            media_type="application/json",
            headers={"WWW-Authenticate": "Bearer"}
        )

# RabbitMQ startup and shutdown handlers
@app.on_event("startup")
async def startup_event():
    """
    Application startup event handler
    """
    try:
        logger.info("Starting application...")

        # Start RabbitMQ event listeners
        logger.info("Starting RabbitMQ event listeners...")
        await event_listener_manager.start_all()

        logger.info("Application startup completed successfully")

    except Exception as e:
        logger.error(f"Error during application startup: {str(e)}", exc_info=True)
        # Don't raise the exception to allow the app to start even if RabbitMQ is not available
        # This ensures the REST API remains functional

@app.on_event("shutdown")
async def shutdown_event():
    """
    Application shutdown event handler
    """
    try:
        logger.info("Shutting down application...")

        # Stop RabbitMQ event listeners
        logger.info("Stopping RabbitMQ event listeners...")
        await event_listener_manager.stop_all()

        logger.info("Application shutdown completed successfully")

    except Exception as e:
        logger.error(f"Error during application shutdown: {str(e)}", exc_info=True)

# Include routers
app.include_router(chatbot.router)
app.include_router(documents.router)

@app.get("/", include_in_schema=False)
async def root():
    return RedirectResponse(url="/docs")

@app.get("/api/swagger.json", include_in_schema=False)
async def get_openapi_json():
    return app.openapi()
