from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Request
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import Document
from app.dependencies import get_auth_context, AuthContext
from app.services.s3_service import S3Service
import logging
import uuid

router = APIRouter(
    prefix="/documents",
    tags=["documents"]
)

logger = logging.getLogger(__name__)

@router.get("/")
async def get_documents(
    request: Request,
    db: Session = Depends(get_db)
):
    """Get all documents for the tenant"""
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    
    # Query documents for the tenant
    documents = db.query(Document).filter(
        Document.tenant_id == tenant_id
    ).all()
    
    return documents

@router.get("/{document_id}")
async def get_document(
    document_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """Get a document by ID"""
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    
    # Query document
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.tenant_id == tenant_id
    ).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Generate download URL if document has S3 key
    if document.s3_key:
        try:
            s3_service = S3Service()
            download_url = s3_service.generate_presigned_url(document.s3_key)
            document_data = document.__dict__.copy()
            document_data["download_url"] = download_url
            return document_data
        except Exception as e:
            logger.error(f"Error generating download URL: {str(e)}")
            # Return document without download URL
            return document
    
    return document

@router.delete("/{document_id}")
async def delete_document(
    document_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """Delete a document by ID"""
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    
    # Query document
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.tenant_id == tenant_id
    ).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Delete from S3 if document has S3 key
    if document.s3_key:
        try:
            s3_service = S3Service()
            s3_service.delete_file(document.s3_key)
        except Exception as e:
            logger.error(f"Error deleting file from S3: {str(e)}")
            # Continue with deletion from database
    
    # Delete from database
    db.delete(document)
    db.commit()
    
    return {"message": "Document deleted successfully"}