from fastapi import Header, HTTPEx<PERSON>, Depends, Request, Security
from typing import Optional, Annotated
from jose import jwt, JWTError
import json
import os
from dotenv import load_dotenv
from pydantic import ValidationError
from app.models import JWTPayload
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

# Load environment variables
load_dotenv()

# JWT configuration
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key")  # Load from environment variables
JWT_ALGORITHM = "HS256"

# Security scheme for Swagger UI
security = HTTPBearer(
    scheme_name="Bearer Authentication",
    description="Enter JWT token",
    auto_error=False,
)

class AuthContext:
    """Class to hold authentication context throughout the request lifecycle"""
    def __init__(self, user_id: str, tenant_id: str, source_type: str, source_id: str, source_name: str, permissions: list):
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.source_type = source_type
        self.source_id = source_id
        self.source_name = source_name
        self.permissions = permissions

async def get_auth_context(
    credentials: HTTPAuthorizationCredentials = Security(security),
    request: Request = None
):
    """
    Extract and validate JWT token from Authorization header.
    Creates an AuthContext object and stores it in request.state for use throughout the request lifecycle.
    """
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Authorization header missing",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token = credentials.credentials
    
    try:
        # Try different approaches to parse the token
        payload = None
        error_messages = []
        
        # Approach 1: Try to decode as JWT
        try:
            payload_dict = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM], options={"verify_signature": False})
            payload = JWTPayload.model_validate(payload_dict)
            print("Successfully decoded JWT token")
        except Exception as e:
            error_messages.append(f"JWT decode error: {str(e)}")
        
        # Approach 2: Try to parse as JSON
        if payload is None:
            try:
                payload_json = json.loads(token)
                payload = JWTPayload.model_validate(payload_json)
                print("Successfully parsed token as JSON")
            except Exception as e:
                error_messages.append(f"JSON parse error: {str(e)}")
        
        # If both approaches failed, raise exception with detailed error messages
        if payload is None:
            raise HTTPException(
                status_code=401,
                detail=f"Failed to parse token: {'; '.join(error_messages)}",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create auth context
        auth_context = AuthContext(
            user_id=payload.data.userId,
            tenant_id=payload.data.tenantId,
            source_type=payload.data.source.type,
            source_id=payload.data.source.id,
            source_name=payload.data.source.name,
            permissions=payload.data.permissions
        )
        
        # Store in request state for use throughout the request lifecycle
        if request:
            request.state.auth_context = auth_context
        
        return auth_context
        
    except Exception as e:
        # Print detailed error for debugging
        import traceback
        print(f"Authentication error: {str(e)}")
        print(traceback.format_exc())
        
        raise HTTPException(
            status_code=401,
            detail=f"Invalid authentication credentials: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )