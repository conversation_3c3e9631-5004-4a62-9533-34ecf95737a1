#!/usr/bin/env python3
"""
Migration script to add new fields to chatbots table:
- welcome_message
- thank_you_message  
- connected_account_id

Run this script to add the new columns to the existing chatbots table.
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import logging

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import get_database_url

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_migration():
    """
    Add new fields to the chatbots table
    """
    try:
        # Get database URL
        database_url = get_database_url()
        logger.info(f"Connecting to database...")
        
        # Create engine
        engine = create_engine(database_url)
        
        # SQL statements to add new columns
        migration_sql = [
            """
            ALTER TABLE chatbots 
            ADD COLUMN IF NOT EXISTS welcome_message VARCHAR;
            """,
            """
            ALTER TABLE chatbots 
            ADD COLUMN IF NOT EXISTS thank_you_message VARCHAR;
            """,
            """
            ALTER TABLE chatbots 
            ADD COLUMN IF NOT EXISTS connected_account_id VARCHAR;
            """
        ]
        
        # Execute migration
        with engine.connect() as connection:
            # Start transaction
            trans = connection.begin()
            
            try:
                logger.info("Starting migration: Adding new chatbot fields...")
                
                for i, sql in enumerate(migration_sql, 1):
                    logger.info(f"Executing migration step {i}/3...")
                    connection.execute(text(sql))
                    logger.info(f"✓ Migration step {i} completed")
                
                # Commit transaction
                trans.commit()
                logger.info("✓ Migration completed successfully!")
                
                # Verify the columns were added
                logger.info("Verifying new columns...")
                result = connection.execute(text("""
                    SELECT column_name, data_type, is_nullable 
                    FROM information_schema.columns 
                    WHERE table_name = 'chatbots' 
                    AND column_name IN ('welcome_message', 'thank_you_message', 'connected_account_id')
                    ORDER BY column_name;
                """))
                
                columns = result.fetchall()
                if len(columns) == 3:
                    logger.info("✓ All new columns verified:")
                    for col in columns:
                        logger.info(f"  - {col[0]} ({col[1]}, nullable: {col[2]})")
                else:
                    logger.warning(f"Expected 3 columns, found {len(columns)}")
                
            except Exception as e:
                # Rollback on error
                trans.rollback()
                logger.error(f"Migration failed, rolling back: {str(e)}")
                raise
                
    except SQLAlchemyError as e:
        logger.error(f"Database error during migration: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during migration: {str(e)}")
        raise


def check_migration_needed():
    """
    Check if the migration is needed by checking if the columns already exist
    """
    try:
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        with engine.connect() as connection:
            result = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'chatbots' 
                AND column_name IN ('welcome_message', 'thank_you_message', 'connected_account_id');
            """))
            
            existing_columns = [row[0] for row in result.fetchall()]
            
            if len(existing_columns) == 3:
                logger.info("Migration not needed - all columns already exist")
                return False
            elif len(existing_columns) == 0:
                logger.info("Migration needed - no new columns found")
                return True
            else:
                logger.info(f"Partial migration detected - {len(existing_columns)}/3 columns exist")
                logger.info(f"Existing columns: {existing_columns}")
                return True
                
    except Exception as e:
        logger.error(f"Error checking migration status: {str(e)}")
        return True  # Assume migration is needed if we can't check


def rollback_migration():
    """
    Rollback the migration by dropping the added columns
    """
    try:
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        rollback_sql = [
            "ALTER TABLE chatbots DROP COLUMN IF EXISTS welcome_message;",
            "ALTER TABLE chatbots DROP COLUMN IF EXISTS thank_you_message;", 
            "ALTER TABLE chatbots DROP COLUMN IF EXISTS connected_account_id;"
        ]
        
        with engine.connect() as connection:
            trans = connection.begin()
            
            try:
                logger.info("Starting rollback: Removing chatbot message fields...")
                
                for i, sql in enumerate(rollback_sql, 1):
                    logger.info(f"Executing rollback step {i}/3...")
                    connection.execute(text(sql))
                    logger.info(f"✓ Rollback step {i} completed")
                
                trans.commit()
                logger.info("✓ Rollback completed successfully!")
                
            except Exception as e:
                trans.rollback()
                logger.error(f"Rollback failed: {str(e)}")
                raise
                
    except Exception as e:
        logger.error(f"Error during rollback: {str(e)}")
        raise


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Chatbot message fields migration")
    parser.add_argument("--rollback", action="store_true", help="Rollback the migration")
    parser.add_argument("--check", action="store_true", help="Check if migration is needed")
    
    args = parser.parse_args()
    
    try:
        if args.rollback:
            rollback_migration()
        elif args.check:
            needed = check_migration_needed()
            sys.exit(0 if not needed else 1)
        else:
            if check_migration_needed():
                run_migration()
            else:
                logger.info("Migration skipped - not needed")
                
    except Exception as e:
        logger.error(f"Migration script failed: {str(e)}")
        sys.exit(1)
