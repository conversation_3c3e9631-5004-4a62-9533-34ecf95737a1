from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database connection parameters
DB_USER = os.getenv("POSTGRES_USER", "sdwhatsapp")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "sdwhatsapp")
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = "5432"
DB_NAME = os.getenv("POSTGRES_DB", "sdwhatsapp")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def run_migration():
    """Add conversation_token_usage table with JSONB structure to track token consumption"""
    engine = create_engine(DATABASE_URL)
    
    with engine.connect() as connection:
        # Check if table already exists
        check_table_query = text("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name='conversation_token_usage'
        """)
        
        table_result = connection.execute(check_table_query)
        if table_result.rowcount == 0:
            # Table doesn't exist, create it
            print("Creating conversation_token_usage table...")
            
            create_table_query = text("""
            CREATE TABLE conversation_token_usage (
                id VARCHAR PRIMARY KEY,
                conversation_id VARCHAR REFERENCES chatbot_conversations(id),
                tenant_id VARCHAR,
                input JSONB,
                output JSONB,
                input_tokens INTEGER DEFAULT 0,
                output_tokens INTEGER DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

                CONSTRAINT fk_conversation
                    FOREIGN KEY(conversation_id)
                    REFERENCES chatbot_conversations(id)
                    ON DELETE CASCADE
            );

            CREATE INDEX idx_conversation_token_usage_conversation_id
                ON conversation_token_usage(conversation_id);

            CREATE INDEX idx_conversation_token_usage_tenant_id
                ON conversation_token_usage(tenant_id);

            CREATE INDEX idx_conversation_token_usage_input_gin
                ON conversation_token_usage USING gin(input);

            CREATE INDEX idx_conversation_token_usage_output_gin
                ON conversation_token_usage USING gin(output);
            """)
            
            connection.execute(create_table_query)
            connection.commit()
            print("Created conversation_token_usage table.")
        else:
            print("Table conversation_token_usage already exists. No changes made.")
        
        print("Migration completed successfully!")

if __name__ == "__main__":
    run_migration()