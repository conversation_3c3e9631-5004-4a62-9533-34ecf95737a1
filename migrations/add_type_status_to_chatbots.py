from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database connection parameters
DB_USER = os.getenv("POSTGRES_USER", "sdwhatsapp")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "sdwhatsapp")
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = "5432"
DB_NAME = os.getenv("POSTGRES_DB", "sdwhatsapp")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def run_migration():
    """Add type and status columns to chatbots table"""
    engine = create_engine(DATABASE_URL)
    
    with engine.connect() as connection:
        # Check if type column already exists
        check_type_query = text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='chatbots' AND column_name='type'
        """)
        
        type_result = connection.execute(check_type_query)
        if type_result.rowcount == 0:
            # Column doesn't exist, add it
            print("Adding type column to chatbots table...")
            
            add_type_query = text("""
            ALTER TABLE chatbots 
            ADD COLUMN type VARCHAR(10) DEFAULT 'AI'
            """)
            
            connection.execute(add_type_query)
            connection.commit()
            print("Added type column to chatbots table.")
        else:
            print("Column type already exists in chatbots table. No changes made.")
        
        # Check if status column already exists
        check_status_query = text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='chatbots' AND column_name='status'
        """)
        
        status_result = connection.execute(check_status_query)
        if status_result.rowcount == 0:
            # Column doesn't exist, add it
            print("Adding status column to chatbots table...")
            
            add_status_query = text("""
            ALTER TABLE chatbots 
            ADD COLUMN status VARCHAR(20) DEFAULT 'DRAFT'
            """)
            
            connection.execute(add_status_query)
            connection.commit()
            print("Added status column to chatbots table.")
        else:
            print("Column status already exists in chatbots table. No changes made.")
        
        print("Migration completed successfully!")

if __name__ == "__main__":
    run_migration()