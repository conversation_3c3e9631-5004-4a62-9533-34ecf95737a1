from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database connection parameters
DB_USER = os.getenv("POSTGRES_USER", "sdwhatsapp")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "sdwhatsapp")
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = "5432"
DB_NAME = os.getenv("POSTGRES_DB", "sdwhatsapp")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def run_migration():
    """Add user_id column to chatbot_conversations table"""
    engine = create_engine(DATABASE_URL)
    
    with engine.connect() as connection:
        # Check if user_id column already exists
        check_column_query = text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='chatbot_conversations' AND column_name='user_id'
        """)
        
        column_result = connection.execute(check_column_query)
        if column_result.rowcount == 0:
            # Column doesn't exist, add it
            print("Adding user_id column to chatbot_conversations table...")
            
            add_column_query = text("""
            ALTER TABLE chatbot_conversations 
            ADD COLUMN user_id VARCHAR;
            
            CREATE INDEX idx_chatbot_conversations_user_id
            ON chatbot_conversations(user_id);
            """)
            
            connection.execute(add_column_query)
            connection.commit()
            print("Added user_id column to chatbot_conversations table.")
        else:
            print("Column user_id already exists in chatbot_conversations table. No changes made.")
        
        print("Migration completed successfully!")

if __name__ == "__main__":
    run_migration()