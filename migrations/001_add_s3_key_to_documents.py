from sqlalchemy import create_engine, Column, String, MetaData, Table, text
import os
from dotenv import load_dotenv

def run_migration():
    """Add s3_key column to documents table"""
    # Load environment variables
    load_dotenv()
    
    # Get database URL from environment
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("DATABASE_URL environment variable not set")
        return
    
    # Create engine and connect to database
    engine = create_engine(database_url)
    
    # Create metadata object
    metadata = MetaData()
    
    # Define documents table
    documents = Table('documents', metadata, autoload_with=engine)
    
    # Check if s3_key column already exists
    if 's3_key' not in [c.name for c in documents.columns]:
        # Add s3_key column
        with engine.begin() as conn:
            conn.execute(text("ALTER TABLE documents ADD COLUMN s3_key VARCHAR;"))
            print("Added s3_key column to documents table")
    else:
        print("s3_key column already exists in documents table")

if __name__ == "__main__":
    run_migration()