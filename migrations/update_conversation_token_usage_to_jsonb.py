#!/usr/bin/env python3
"""
Migration script to update conversation_token_usage table to use JSONB columns
This migration handles both new installations and existing table updates.
"""

from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get database connection parameters
DB_USER = os.getenv("POSTGRES_USER", "sdwhatsapp")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "sdwhatsapp")
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = "5432"
DB_NAME = os.getenv("POSTGRES_DB", "sdwhatsapp")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def check_table_exists(connection):
    """Check if conversation_token_usage table exists"""
    check_table_query = text("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name = 'conversation_token_usage'
    """)
    result = connection.execute(check_table_query)
    return result.rowcount > 0

def get_table_columns(connection):
    """Get current table columns"""
    columns_query = text("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'conversation_token_usage'
        ORDER BY ordinal_position
    """)
    result = connection.execute(columns_query)
    return {row[0]: row[1] for row in result.fetchall()}

def backup_existing_data(connection):
    """Backup existing data if table has old structure"""
    logger.info("Backing up existing conversation_token_usage data...")
    
    # Create backup table
    backup_query = text("""
        CREATE TABLE conversation_token_usage_backup AS 
        SELECT * FROM conversation_token_usage;
    """)
    connection.execute(backup_query)
    logger.info("Data backed up to conversation_token_usage_backup table")

def migrate_old_data_to_jsonb(connection):
    """Migrate old data structure to new JSONB structure"""
    logger.info("Migrating old data to new JSONB structure...")
    
    # Get old data
    select_old_data = text("""
        SELECT id, conversation_id, tenant_id, message_type, message_content, 
               input_tokens, output_tokens, model, timestamp
        FROM conversation_token_usage_backup
        ORDER BY timestamp
    """)
    
    old_records = connection.execute(select_old_data).fetchall()
    
    if not old_records:
        logger.info("No old data to migrate")
        return
    
    logger.info(f"Found {len(old_records)} old records to migrate")
    
    # Group records by conversation to reconstruct conversation turns
    conversations = {}
    for record in old_records:
        conv_id = record[1]  # conversation_id
        if conv_id not in conversations:
            conversations[conv_id] = []
        conversations[conv_id].append(record)
    
    migrated_count = 0
    
    for conv_id, records in conversations.items():
        # Process records in pairs (user + assistant) or individually
        i = 0
        while i < len(records):
            current_record = records[i]
            message_type = current_record[3]  # message_type
            
            if message_type == 'user':
                # Look for corresponding assistant response
                user_record = current_record
                assistant_record = None
                
                # Find the next assistant record
                for j in range(i + 1, len(records)):
                    if records[j][3] == 'assistant':  # message_type
                        assistant_record = records[j]
                        break
                
                if assistant_record:
                    # Create conversation turn from user + assistant pair
                    input_data = {
                        "user": user_record[4],  # message_content
                        "system": "System prompt not available in old data"
                    }
                    output_data = {
                        "assistant": assistant_record[4]  # message_content
                    }
                    
                    insert_query = text("""
                        INSERT INTO conversation_token_usage 
                        (id, conversation_id, tenant_id, input, output, input_tokens, output_tokens, timestamp)
                        VALUES (:id, :conversation_id, :tenant_id, :input, :output, :input_tokens, :output_tokens, :timestamp)
                    """)
                    
                    connection.execute(insert_query, {
                        'id': user_record[0],  # Use user record ID
                        'conversation_id': user_record[1],
                        'tenant_id': user_record[2],
                        'input': input_data,
                        'output': output_data,
                        'input_tokens': assistant_record[5] or 0,  # input_tokens from assistant record
                        'output_tokens': assistant_record[6] or 0,  # output_tokens from assistant record
                        'timestamp': user_record[8]  # timestamp from user record
                    })
                    migrated_count += 1
                    
                    # Skip the assistant record in next iteration
                    if j == i + 1:
                        i += 2
                    else:
                        i += 1
                else:
                    # User record without assistant response
                    input_data = {
                        "user": user_record[4],
                        "system": "System prompt not available in old data"
                    }
                    output_data = {
                        "assistant": "No response recorded"
                    }
                    
                    insert_query = text("""
                        INSERT INTO conversation_token_usage 
                        (id, conversation_id, tenant_id, input, output, input_tokens, output_tokens, timestamp)
                        VALUES (:id, :conversation_id, :tenant_id, :input, :output, :input_tokens, :output_tokens, :timestamp)
                    """)
                    
                    connection.execute(insert_query, {
                        'id': user_record[0],
                        'conversation_id': user_record[1],
                        'tenant_id': user_record[2],
                        'input': input_data,
                        'output': output_data,
                        'input_tokens': user_record[5] or 0,
                        'output_tokens': 0,
                        'timestamp': user_record[8]
                    })
                    migrated_count += 1
                    i += 1
            
            elif message_type == 'assistant':
                # Standalone assistant record (shouldn't happen in normal flow)
                input_data = {
                    "user": "User message not available",
                    "system": "System prompt not available in old data"
                }
                output_data = {
                    "assistant": current_record[4]
                }
                
                insert_query = text("""
                    INSERT INTO conversation_token_usage 
                    (id, conversation_id, tenant_id, input, output, input_tokens, output_tokens, timestamp)
                    VALUES (:id, :conversation_id, :tenant_id, :input, :output, :input_tokens, :output_tokens, :timestamp)
                """)
                
                connection.execute(insert_query, {
                    'id': current_record[0],
                    'conversation_id': current_record[1],
                    'tenant_id': current_record[2],
                    'input': input_data,
                    'output': output_data,
                    'input_tokens': current_record[5] or 0,
                    'output_tokens': current_record[6] or 0,
                    'timestamp': current_record[8]
                })
                migrated_count += 1
                i += 1
            
            else:
                # Skip system or other message types
                i += 1
    
    logger.info(f"Successfully migrated {migrated_count} conversation turns")

def create_new_table(connection):
    """Create new conversation_token_usage table with JSONB structure"""
    logger.info("Creating new conversation_token_usage table...")
    
    create_table_query = text("""
        CREATE TABLE conversation_token_usage (
            id VARCHAR PRIMARY KEY,
            conversation_id VARCHAR REFERENCES chatbot_conversations(id),
            tenant_id VARCHAR,
            input JSONB,
            output JSONB,
            input_tokens INTEGER DEFAULT 0,
            output_tokens INTEGER DEFAULT 0,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            CONSTRAINT fk_conversation
                FOREIGN KEY(conversation_id)
                REFERENCES chatbot_conversations(id)
                ON DELETE CASCADE
        );
        
        CREATE INDEX idx_conversation_token_usage_conversation_id
            ON conversation_token_usage(conversation_id);
            
        CREATE INDEX idx_conversation_token_usage_tenant_id
            ON conversation_token_usage(tenant_id);
            
        CREATE INDEX idx_conversation_token_usage_input_gin
            ON conversation_token_usage USING gin(input);
            
        CREATE INDEX idx_conversation_token_usage_output_gin
            ON conversation_token_usage USING gin(output);
    """)
    
    connection.execute(create_table_query)
    logger.info("New conversation_token_usage table created with JSONB columns and indexes")

def update_existing_table(connection):
    """Update existing table structure to JSONB"""
    logger.info("Updating existing table structure...")
    
    # First, backup existing data
    backup_existing_data(connection)
    
    # Drop the old table
    drop_table_query = text("DROP TABLE conversation_token_usage CASCADE;")
    connection.execute(drop_table_query)
    logger.info("Dropped old conversation_token_usage table")
    
    # Create new table
    create_new_table(connection)
    
    # Migrate old data
    migrate_old_data_to_jsonb(connection)
    
    logger.info("Table structure updated successfully")

def run_migration():
    """Run the migration to update conversation_token_usage table"""
    engine = create_engine(DATABASE_URL)
    
    with engine.begin() as connection:
        logger.info("Starting conversation_token_usage table migration...")
        
        # Check if table exists
        table_exists = check_table_exists(connection)
        
        if not table_exists:
            # Table doesn't exist, create new one
            logger.info("Table doesn't exist. Creating new conversation_token_usage table...")
            create_new_table(connection)
            logger.info("New table created successfully!")
        
        else:
            # Table exists, check its structure
            columns = get_table_columns(connection)
            logger.info(f"Found existing table with columns: {list(columns.keys())}")
            
            # Check if it already has the new JSONB structure
            if 'input' in columns and 'output' in columns and columns['input'] == 'jsonb':
                logger.info("Table already has JSONB structure. No migration needed.")
            
            elif 'message_type' in columns and 'message_content' in columns:
                # Old structure, needs migration
                logger.info("Found old table structure. Starting migration...")
                update_existing_table(connection)
                logger.info("Migration completed successfully!")
            
            else:
                # Unknown structure
                logger.warning("Unknown table structure. Please check manually.")
                logger.info(f"Current columns: {columns}")
                return False
        
        logger.info("Migration completed successfully!")
        return True

if __name__ == "__main__":
    print("=" * 60)
    print("CONVERSATION TOKEN USAGE TABLE MIGRATION")
    print("=" * 60)
    
    try:
        success = run_migration()
        if success:
            print("\n🎉 Migration completed successfully!")
            print("\nNew table structure:")
            print("- id (VARCHAR PRIMARY KEY)")
            print("- conversation_id (VARCHAR)")
            print("- tenant_id (VARCHAR)")
            print("- input (JSONB) - User message and system prompt")
            print("- output (JSONB) - AI-generated response")
            print("- input_tokens (INTEGER)")
            print("- output_tokens (INTEGER)")
            print("- timestamp (TIMESTAMP)")
            print("\nIndexes created:")
            print("- conversation_id index")
            print("- tenant_id index")
            print("- input JSONB GIN index")
            print("- output JSONB GIN index")
        else:
            print("\n❌ Migration failed. Please check the logs.")
    except Exception as e:
        print(f"\n❌ Migration failed with error: {str(e)}")
        logger.error(f"Migration failed: {str(e)}", exc_info=True)
