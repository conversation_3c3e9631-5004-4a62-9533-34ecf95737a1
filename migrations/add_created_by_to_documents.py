from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database connection parameters
DB_USER = os.getenv("POSTGRES_USER", "sdwhatsapp")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "sdwhatsapp")
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = "5432"
DB_NAME = os.getenv("POSTGRES_DB", "sdwhatsapp")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def run_migration():
    """Add created_by column to documents table"""
    engine = create_engine(DATABASE_URL)
    
    with engine.connect() as connection:
        # Check if column already exists
        check_query = text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='documents' AND column_name='created_by'
        """)
        
        result = connection.execute(check_query)
        if result.rowcount == 0:
            # Column doesn't exist, add it
            print("Adding created_by column to documents table...")
            
            add_column_query = text("""
            ALTER TABLE documents 
            ADD COLUMN created_by INTEGER NULL
            """)
            
            connection.execute(add_column_query)
            connection.commit()
            print("Migration completed successfully!")
        else:
            print("Column created_by already exists in documents table. No changes made.")

if __name__ == "__main__":
    run_migration()