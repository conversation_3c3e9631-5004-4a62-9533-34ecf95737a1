#!/usr/bin/env python3
"""
Test script to verify the ChatbotService CRUD operations refactoring
"""

import os
import sys
import logging
from unittest.mock import MagicMock, patch, AsyncMock

# Set up basic logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_chatbot_service_crud_methods():
    """Test that ChatbotService has all CRUD methods"""
    print("Testing ChatbotService CRUD methods...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        service = ChatbotService()
        
        crud_methods = [
            'create_chatbot',
            'update_chatbot',
            'delete_chatbot',
            'get_chatbot_with_details',
            'list_chatbots',
            'delete_question',
            'configure_chatbot_questions',
            'get_chatbot_for_conversation',
            'get_chatbot_questions_for_conversation'
        ]
        
        found_methods = []
        for method_name in crud_methods:
            if hasattr(service, method_name):
                method = getattr(service, method_name)
                if callable(method):
                    found_methods.append(method_name)
                    print(f"  ✓ {method_name} method found")
                else:
                    print(f"  ✗ {method_name} is not callable")
            else:
                print(f"  ✗ {method_name} method not found")
        
        if len(found_methods) == len(crud_methods):
            print("✓ All CRUD methods are present")
            return True
        else:
            print(f"✗ Missing {len(crud_methods) - len(found_methods)} CRUD methods")
            return False
            
    except Exception as e:
        print(f"✗ CRUD methods test failed: {str(e)}")
        return False


def test_router_imports_chatbot_service():
    """Test that the router imports ChatbotService"""
    print("\nTesting router imports ChatbotService...")
    
    try:
        with open('app/routers/chatbot.py', 'r') as f:
            content = f.read()
        
        if 'from app.services.chatbot_service import ChatbotService' in content:
            print("✓ Router imports ChatbotService")
            return True
        else:
            print("✗ Router does not import ChatbotService")
            return False
            
    except FileNotFoundError:
        print("✗ chatbot.py router not found")
        return False
    except Exception as e:
        print(f"✗ Import test failed: {str(e)}")
        return False


def test_router_uses_service():
    """Test that router endpoints use ChatbotService instead of direct DB operations"""
    print("\nTesting router uses ChatbotService...")
    
    try:
        with open('app/routers/chatbot.py', 'r') as f:
            content = f.read()
        
        # Check for service usage patterns
        service_usage_patterns = [
            'chatbot_service = ChatbotService()',
            'chatbot_service.create_chatbot',
            'chatbot_service.update_chatbot',
            'chatbot_service.delete_chatbot',
            'chatbot_service.get_chatbot_with_details',
            'chatbot_service.list_chatbots'
        ]
        
        found_patterns = []
        for pattern in service_usage_patterns:
            if pattern in content:
                found_patterns.append(pattern)
                print(f"  ✓ {pattern}")
            else:
                print(f"  ✗ {pattern} not found")
        
        # Check that direct DB operations are reduced
        direct_db_patterns = [
            'db.query(Chatbot).filter(',
            'db.add(chatbot)',
            'db.delete(chatbot)'
        ]
        
        remaining_db_operations = []
        for pattern in direct_db_patterns:
            if pattern in content:
                remaining_db_operations.append(pattern)
        
        print(f"  - Found {len(found_patterns)} service usage patterns")
        print(f"  - Found {len(remaining_db_operations)} remaining direct DB operations")
        
        if len(found_patterns) >= 4:  # At least 4 service patterns should be present
            print("✓ Router properly uses ChatbotService")
            return True
        else:
            print("✗ Router does not sufficiently use ChatbotService")
            return False
            
    except Exception as e:
        print(f"✗ Service usage test failed: {str(e)}")
        return False


def test_endpoint_structure():
    """Test that endpoints have clean structure with minimal logic"""
    print("\nTesting endpoint structure...")
    
    try:
        with open('app/routers/chatbot.py', 'r') as f:
            content = f.read()
        
        # Check for clean endpoint patterns
        clean_patterns = [
            'auth_context = request.state.auth_context',
            'tenant_id = auth_context.tenant_id',
            'return chatbot_service.',
        ]
        
        found_clean_patterns = []
        for pattern in clean_patterns:
            if pattern in content:
                found_clean_patterns.append(pattern)
                print(f"  ✓ {pattern}")
            else:
                print(f"  ✗ {pattern} not found")
        
        # Check that endpoints are concise (should have fewer lines now)
        endpoint_lines = content.count('@router.')
        total_lines = len(content.split('\n'))
        avg_lines_per_endpoint = total_lines / endpoint_lines if endpoint_lines > 0 else 0
        
        print(f"  - Found {endpoint_lines} endpoints")
        print(f"  - Average lines per endpoint: {avg_lines_per_endpoint:.1f}")
        
        if len(found_clean_patterns) >= 2 and avg_lines_per_endpoint < 150:
            print("✓ Endpoints have clean structure")
            return True
        else:
            print("✗ Endpoints structure needs improvement")
            return False
            
    except Exception as e:
        print(f"✗ Endpoint structure test failed: {str(e)}")
        return False


def test_new_list_endpoint():
    """Test that the new list chatbots endpoint exists"""
    print("\nTesting new list chatbots endpoint...")
    
    try:
        with open('app/routers/chatbot.py', 'r') as f:
            content = f.read()
        
        list_endpoint_patterns = [
            '@router.get("/")',
            'async def list_chatbots',
            'include_draft: bool = True',
            'chatbot_service.list_chatbots'
        ]
        
        found_patterns = []
        for pattern in list_endpoint_patterns:
            if pattern in content:
                found_patterns.append(pattern)
                print(f"  ✓ {pattern}")
            else:
                print(f"  ✗ {pattern} not found")
        
        if len(found_patterns) == len(list_endpoint_patterns):
            print("✓ List chatbots endpoint is properly implemented")
            return True
        else:
            print(f"✗ Missing {len(list_endpoint_patterns) - len(found_patterns)} list endpoint components")
            return False
            
    except Exception as e:
        print(f"✗ List endpoint test failed: {str(e)}")
        return False


def test_service_error_handling():
    """Test that service methods have proper error handling"""
    print("\nTesting service error handling...")
    
    try:
        with open('app/services/chatbot_service.py', 'r') as f:
            content = f.read()
        
        error_handling_patterns = [
            'try:',
            'except HTTPException:',
            'except Exception as e:',
            'raise HTTPException',
            'finally:',
            'if db:',
            'db.close()'
        ]
        
        found_patterns = []
        for pattern in error_handling_patterns:
            if pattern in content:
                found_patterns.append(pattern)
                print(f"  ✓ {pattern}")
            else:
                print(f"  ✗ {pattern} not found")
        
        if len(found_patterns) >= 6:  # Most error handling patterns should be present
            print("✓ Service has proper error handling")
            return True
        else:
            print("✗ Service error handling is insufficient")
            return False
            
    except Exception as e:
        print(f"✗ Error handling test failed: {str(e)}")
        return False


def test_imports_and_dependencies():
    """Test that imports and dependencies are properly organized"""
    print("\nTesting imports and dependencies...")
    
    try:
        # Check ChatbotService imports
        with open('app/services/chatbot_service.py', 'r') as f:
            service_content = f.read()
        
        service_imports = [
            'from fastapi import HTTPException',
            'from app.models import (',
            'ChatbotCreate',
            'ChatbotUpdate',
            'QuestionCreate'
        ]
        
        service_import_found = []
        for imp in service_imports:
            if imp in service_content:
                service_import_found.append(imp)
                print(f"  ✓ Service: {imp}")
            else:
                print(f"  ✗ Service: {imp} not found")
        
        # Check router imports
        with open('app/routers/chatbot.py', 'r') as f:
            router_content = f.read()
        
        router_imports = [
            'from app.services.chatbot_service import ChatbotService'
        ]
        
        router_import_found = []
        for imp in router_imports:
            if imp in router_content:
                router_import_found.append(imp)
                print(f"  ✓ Router: {imp}")
            else:
                print(f"  ✗ Router: {imp} not found")
        
        total_found = len(service_import_found) + len(router_import_found)
        total_expected = len(service_imports) + len(router_imports)
        
        if total_found >= total_expected - 1:  # Allow some flexibility
            print("✓ Imports and dependencies are properly organized")
            return True
        else:
            print("✗ Some imports are missing")
            return False
            
    except Exception as e:
        print(f"✗ Imports test failed: {str(e)}")
        return False


def main():
    """Run all CRUD refactoring tests"""
    print("=" * 70)
    print("ChatbotService CRUD Refactoring Test Suite")
    print("=" * 70)
    
    tests = [
        test_chatbot_service_crud_methods,
        test_router_imports_chatbot_service,
        test_router_uses_service,
        test_endpoint_structure,
        test_new_list_endpoint,
        test_service_error_handling,
        test_imports_and_dependencies
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed: {str(e)}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! CRUD refactoring is successful.")
        print("\nRefactoring Summary:")
        print("  • All CRUD operations moved to ChatbotService")
        print("  • Router endpoints simplified to route mapping only")
        print("  • Proper error handling and logging in service")
        print("  • New list chatbots endpoint added")
        print("  • Clean separation of concerns achieved")
    else:
        print("✗ Some tests failed. Please review the refactoring.")
    
    print("=" * 70)


if __name__ == "__main__":
    main()
