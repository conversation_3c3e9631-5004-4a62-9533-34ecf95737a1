#!/usr/bin/env python3
"""
Comprehensive migration runner for conversation_token_usage table.
This script automatically detects the current state and runs the appropriate migration.
"""

import os
import sys
from sqlalchemy import create_engine, text
from dotenv import load_dotenv
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get database connection parameters
DB_USER = os.getenv("POSTGRES_USER", "sdwhatsapp")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "sdwhatsapp")
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = "5432"
DB_NAME = os.getenv("POSTGRES_DB", "sdwhatsapp")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def test_database_connection():
    """Test database connection"""
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        logger.info("✅ Database connection successful")
        return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {str(e)}")
        return False

def check_table_status():
    """Check the current status of conversation_token_usage table"""
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as connection:
            # Check if table exists
            check_table_query = text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name = 'conversation_token_usage'
            """)
            result = connection.execute(check_table_query)
            
            if result.rowcount == 0:
                return "not_exists"
            
            # Table exists, check its structure
            columns_query = text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'conversation_token_usage'
                ORDER BY ordinal_position
            """)
            result = connection.execute(columns_query)
            columns = {row[0]: row[1] for row in result.fetchall()}
            
            logger.info(f"Found table with columns: {list(columns.keys())}")
            
            # Check if it has JSONB structure
            if 'input' in columns and 'output' in columns:
                if columns.get('input') == 'jsonb' or 'json' in columns.get('input', '').lower():
                    return "jsonb_structure"
                else:
                    return "unknown_structure"
            
            # Check if it has old structure
            elif 'message_type' in columns and 'message_content' in columns:
                return "old_structure"
            
            else:
                return "unknown_structure"
                
    except Exception as e:
        logger.error(f"Error checking table status: {str(e)}")
        return "error"

def run_fresh_installation():
    """Run migration for fresh installation"""
    logger.info("Running fresh installation migration...")
    
    try:
        # Import and run the fresh installation migration
        sys.path.append('migrations')
        from add_conversation_token_usage import run_migration
        
        success = run_migration()
        if success:
            logger.info("✅ Fresh installation completed successfully")
            return True
        else:
            logger.error("❌ Fresh installation failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Fresh installation failed: {str(e)}")
        return False

def run_structure_update():
    """Run migration to update existing table structure"""
    logger.info("Running structure update migration...")
    
    try:
        # Import and run the structure update migration
        sys.path.append('migrations')
        from update_conversation_token_usage_to_jsonb import run_migration
        
        success = run_migration()
        if success:
            logger.info("✅ Structure update completed successfully")
            return True
        else:
            logger.error("❌ Structure update failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Structure update failed: {str(e)}")
        return False

def verify_final_structure():
    """Verify the final table structure"""
    logger.info("Verifying final table structure...")
    
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as connection:
            # Check table structure
            columns_query = text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'conversation_token_usage'
                ORDER BY ordinal_position
            """)
            result = connection.execute(columns_query)
            columns = result.fetchall()
            
            logger.info("Final table structure:")
            for col in columns:
                logger.info(f"  - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
            
            # Check indexes
            indexes_query = text("""
                SELECT indexname, indexdef
                FROM pg_indexes 
                WHERE tablename = 'conversation_token_usage'
            """)
            result = connection.execute(indexes_query)
            indexes = result.fetchall()
            
            logger.info("Indexes:")
            for idx in indexes:
                logger.info(f"  - {idx[0]}")
            
            # Verify required columns exist
            column_names = [col[0] for col in columns]
            required_columns = ['id', 'conversation_id', 'tenant_id', 'input', 'output', 'input_tokens', 'output_tokens', 'timestamp']
            
            missing_columns = [col for col in required_columns if col not in column_names]
            if missing_columns:
                logger.error(f"❌ Missing required columns: {missing_columns}")
                return False
            
            # Check JSONB columns
            jsonb_columns = [col[0] for col in columns if 'json' in col[1].lower()]
            if 'input' not in jsonb_columns or 'output' not in jsonb_columns:
                logger.error("❌ Input and output columns are not JSONB type")
                return False
            
            logger.info("✅ Table structure verification passed")
            return True
            
    except Exception as e:
        logger.error(f"❌ Structure verification failed: {str(e)}")
        return False

def main():
    """Main migration function"""
    print("=" * 70)
    print("CONVERSATION TOKEN USAGE TABLE MIGRATION")
    print("=" * 70)
    
    # Test database connection
    if not test_database_connection():
        print("\n❌ Cannot connect to database. Please check your connection settings.")
        return False
    
    # Check current table status
    logger.info("Checking current table status...")
    status = check_table_status()
    
    print(f"\nCurrent table status: {status}")
    
    # Run appropriate migration based on status
    success = False
    
    if status == "not_exists":
        print("\n📋 Table doesn't exist. Running fresh installation...")
        success = run_fresh_installation()
        
    elif status == "old_structure":
        print("\n🔄 Found old table structure. Running structure update...")
        print("⚠️  This will backup existing data and update the table structure.")
        
        # Ask for confirmation
        response = input("\nDo you want to proceed? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            success = run_structure_update()
        else:
            print("Migration cancelled by user.")
            return False
            
    elif status == "jsonb_structure":
        print("\n✅ Table already has JSONB structure. No migration needed.")
        success = verify_final_structure()
        
    elif status == "unknown_structure":
        print("\n⚠️  Unknown table structure detected.")
        print("Please check the table manually or contact support.")
        return False
        
    elif status == "error":
        print("\n❌ Error checking table status.")
        return False
    
    # Verify final structure
    if success:
        print("\n🔍 Verifying final structure...")
        if verify_final_structure():
            print("\n🎉 Migration completed successfully!")
            print("\nFinal table structure:")
            print("- id (VARCHAR PRIMARY KEY)")
            print("- conversation_id (VARCHAR)")
            print("- tenant_id (VARCHAR)")
            print("- input (JSONB) - User message and system prompt")
            print("- output (JSONB) - AI-generated response")
            print("- input_tokens (INTEGER)")
            print("- output_tokens (INTEGER)")
            print("- timestamp (TIMESTAMP)")
            print("\nYour conversation token usage tracking is now ready!")
            return True
        else:
            print("\n❌ Structure verification failed.")
            return False
    else:
        print("\n❌ Migration failed.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nMigration cancelled by user.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)
