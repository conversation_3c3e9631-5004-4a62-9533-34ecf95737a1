# Database Migration Guide - Missing Chatbot Columns

## Problem Description

The application code in `app/models.py` defines a `Chatbot` model with the following columns:

```python
class Chatbot(Base):
    __tablename__ = "chatbots"
    
    id = Column(String, primary_key=True, index=True)
    tenant_id = Column(String, index=True)
    name = Column(String)
    type = Column(String, default="AI")
    description = Column(String, nullable=True)
    status = Column(String, default="DRAFT")
    welcome_message = Column(String, nullable=True)        # ← MISSING IN DB
    thank_you_message = Column(String, nullable=True)      # ← MISSING IN DB
    connected_account_id = Column(String, nullable=True)   # ← MISSING IN DB
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

However, the database table `chatbots` is missing these three columns:
- `welcome_message`
- `thank_you_message`
- `connected_account_id`

This causes errors when the application tries to create or update chatbots with these fields.

## Error Symptoms

You may see errors like:
```
column chatbots.welcome_message does not exist
column chatbots.thank_you_message does not exist
column chatbots.connected_account_id does not exist
```

## Solution Options

### Option 1: Automated Python Migration (Recommended)

Run the Python migration script:

```bash
python3 run_chatbot_migration.py
```

This script will:
- Check if the columns already exist
- Add missing columns safely using `ADD COLUMN IF NOT EXISTS`
- Verify the migration was successful
- Provide detailed logging

**Requirements:**
- Python 3.x
- psycopg2-binary package (`pip install psycopg2-binary`)
- Database connection environment variables

### Option 2: Manual SQL Migration

If you prefer to run the migration manually or the Python script doesn't work:

1. **Connect to your PostgreSQL database:**
   ```bash
   psql -h localhost -U sdwhatsapp -d sdwhatsapp
   ```

2. **Run the SQL migration file:**
   ```bash
   \i add_chatbot_columns.sql
   ```

3. **Or execute the SQL commands directly:**
   ```sql
   ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS welcome_message VARCHAR;
   ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS thank_you_message VARCHAR;
   ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS connected_account_id VARCHAR;
   ```

4. **Verify the columns were added:**
   ```sql
   SELECT column_name, data_type, is_nullable
   FROM information_schema.columns
   WHERE table_name = 'chatbots'
   AND column_name IN ('welcome_message', 'thank_you_message', 'connected_account_id');
   ```

### Option 3: Using Existing Migration Script

There's already a migration script in the project:

```bash
python3 migrations/add_chatbot_message_fields.py
```

**Note:** This requires the full application dependencies to be installed.

## Database Environment Variables

Make sure these environment variables are set correctly:

```bash
POSTGRES_USER=sdwhatsapp
POSTGRES_PASSWORD=sdwhatsapp
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=sdwhatsapp
```

You can set these in a `.env` file in the project root:

```env
POSTGRES_USER=sdwhatsapp
POSTGRES_PASSWORD=sdwhatsapp
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=sdwhatsapp
```

## Verification

After running the migration, verify the columns exist:

### Using SQL:
```sql
\d chatbots
```

Or:
```sql
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'chatbots'
ORDER BY ordinal_position;
```

### Expected Result:
You should see these columns in the chatbots table:
- `welcome_message` (VARCHAR, nullable)
- `thank_you_message` (VARCHAR, nullable)
- `connected_account_id` (VARCHAR, nullable)

## Testing the Fix

After the migration, test that the application works:

1. **Create a new chatbot with welcome message:**
   ```bash
   curl -X POST "http://localhost:8000/chatbot/" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-token" \
     -d '{
       "name": "Test Chatbot",
       "type": "AI",
       "description": "Test chatbot",
       "welcome_message": "Hello! Welcome to our chatbot.",
       "thank_you_message": "Thank you for using our service!",
       "connected_account_id": "account-123"
     }'
   ```

2. **Update an existing chatbot:**
   ```bash
   curl -X PUT "http://localhost:8000/chatbot/{chatbot_id}" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-token" \
     -d '{
       "welcome_message": "Updated welcome message"
     }'
   ```

3. **Verify the data is stored correctly:**
   ```sql
   SELECT id, name, welcome_message, thank_you_message, connected_account_id
   FROM chatbots
   WHERE welcome_message IS NOT NULL;
   ```

## Rollback (If Needed)

If you need to rollback the migration:

```sql
ALTER TABLE chatbots DROP COLUMN IF EXISTS welcome_message;
ALTER TABLE chatbots DROP COLUMN IF EXISTS thank_you_message;
ALTER TABLE chatbots DROP COLUMN IF EXISTS connected_account_id;
```

Or use the existing rollback script:
```bash
python3 migrations/add_chatbot_message_fields.py --rollback
```

## Files Created

This migration guide includes these files:

1. **`add_chatbot_columns.sql`** - Pure SQL migration script
2. **`run_chatbot_migration.py`** - Standalone Python migration script
3. **`DATABASE_MIGRATION_GUIDE.md`** - This documentation file

## Troubleshooting

### Common Issues:

1. **"psycopg2 module not found"**
   ```bash
   pip install psycopg2-binary
   ```

2. **"Connection refused"**
   - Check if PostgreSQL is running
   - Verify connection parameters in environment variables
   - Check firewall settings

3. **"Permission denied"**
   - Ensure the database user has ALTER TABLE permissions
   - Connect as a superuser if needed

4. **"Table 'chatbots' does not exist"**
   - Create the chatbots table first using the full application setup
   - Run initial database migrations

### Getting Help:

If you encounter issues:
1. Check the application logs for detailed error messages
2. Verify database connectivity with `psql`
3. Ensure all environment variables are set correctly
4. Check PostgreSQL server logs for additional details

## Summary

This migration adds three nullable VARCHAR columns to the `chatbots` table to match the application model definition. The migration is safe to run multiple times and will not affect existing data.

After running the migration, the chatbot functionality will work correctly with welcome messages, thank you messages, and connected account IDs.
